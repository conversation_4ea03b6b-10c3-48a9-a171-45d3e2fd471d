<?php
/**
 * Check Hero Sections Newsletter Database Structure
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>🔧 HERO SECTIONS NEWSLETTER DEBUG</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:4px;}</style>";

try {
    $db = Database::getConnection();
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // Check hero_sections table structure
    echo "<h2>📋 Step 1: Check Hero Sections Table Structure</h2>";
    
    $stmt = $db->prepare("DESCRIBE hero_sections");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<div class='info'>📊 Hero Sections Table Columns:</div>";
    echo "<pre>";
    
    $newsletter_fields = ['show_newsletter_input', 'newsletter_placeholder', 'newsletter_button_text', 'newsletter_success_message'];
    $found_fields = [];
    
    foreach ($columns as $column) {
        $field_name = $column['Field'];
        $is_newsletter = in_array($field_name, $newsletter_fields);
        $marker = $is_newsletter ? "📧" : "  ";
        
        echo "$marker $field_name: {$column['Type']} " . 
             ($column['Null'] === 'NO' ? '(NOT NULL)' : '(NULL)') . 
             ($column['Key'] ? " [{$column['Key']}]" : '') . "\n";
             
        if ($is_newsletter) {
            $found_fields[] = $field_name;
        }
    }
    echo "</pre>";
    
    // Check if newsletter fields exist
    echo "<h2>📋 Step 2: Newsletter Fields Check</h2>";
    
    foreach ($newsletter_fields as $field) {
        if (in_array($field, $found_fields)) {
            echo "<div class='success'>✅ $field exists</div>";
        } else {
            echo "<div class='error'>❌ $field missing</div>";
            
            // Add missing field
            echo "<div class='info'>🔧 Adding $field...</div>";
            
            switch ($field) {
                case 'show_newsletter_input':
                    $db->exec("ALTER TABLE hero_sections ADD COLUMN show_newsletter_input BOOLEAN DEFAULT FALSE");
                    break;
                case 'newsletter_placeholder':
                    $db->exec("ALTER TABLE hero_sections ADD COLUMN newsletter_placeholder VARCHAR(255) DEFAULT 'Enter your email address'");
                    break;
                case 'newsletter_button_text':
                    $db->exec("ALTER TABLE hero_sections ADD COLUMN newsletter_button_text VARCHAR(100) DEFAULT 'Subscribe'");
                    break;
                case 'newsletter_success_message':
                    $db->exec("ALTER TABLE hero_sections ADD COLUMN newsletter_success_message VARCHAR(255) DEFAULT 'Thank you for subscribing!'");
                    break;
            }
            
            echo "<div class='success'>✅ $field added successfully</div>";
        }
    }
    
    // Check existing hero sections with newsletter settings
    echo "<h2>📋 Step 3: Current Hero Sections with Newsletter Settings</h2>";
    
    $stmt = $db->prepare("SELECT page_name, page_title, show_newsletter_input, newsletter_placeholder, newsletter_button_text, newsletter_success_message FROM hero_sections ORDER BY page_name");
    $stmt->execute();
    $heroes = $stmt->fetchAll();
    
    if ($heroes) {
        echo "<div class='info'>📊 Hero Sections:</div>";
        echo "<pre>";
        foreach ($heroes as $hero) {
            $newsletter_enabled = $hero['show_newsletter_input'] ? 'YES' : 'NO';
            echo "📄 {$hero['page_name']} ({$hero['page_title']})\n";
            echo "   📧 Newsletter Enabled: $newsletter_enabled\n";
            if ($hero['show_newsletter_input']) {
                echo "   📝 Placeholder: {$hero['newsletter_placeholder']}\n";
                echo "   🔘 Button Text: {$hero['newsletter_button_text']}\n";
                echo "   ✅ Success Message: {$hero['newsletter_success_message']}\n";
            }
            echo "\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='info'>ℹ️ No hero sections found</div>";
    }
    
    // Test contact page hero section specifically
    echo "<h2>📋 Step 4: Contact Page Hero Section Test</h2>";
    
    $stmt = $db->prepare("SELECT * FROM hero_sections WHERE page_name = 'contact'");
    $stmt->execute();
    $contact_hero = $stmt->fetch();
    
    if ($contact_hero) {
        echo "<div class='success'>✅ Contact page hero section found</div>";
        echo "<pre>";
        echo "Page: {$contact_hero['page_name']}\n";
        echo "Title: {$contact_hero['page_title']}\n";
        echo "Active: " . ($contact_hero['active'] ? 'YES' : 'NO') . "\n";
        echo "Newsletter Enabled: " . ($contact_hero['show_newsletter_input'] ? 'YES' : 'NO') . "\n";
        
        if ($contact_hero['show_newsletter_input']) {
            echo "Newsletter Placeholder: {$contact_hero['newsletter_placeholder']}\n";
            echo "Newsletter Button Text: {$contact_hero['newsletter_button_text']}\n";
            echo "Newsletter Success Message: {$contact_hero['newsletter_success_message']}\n";
        }
        echo "</pre>";
        
        if (!$contact_hero['show_newsletter_input']) {
            echo "<div class='error'>❌ Newsletter signup is not enabled for contact page</div>";
            echo "<div class='info'>💡 Go to admin/hero-sections.php and edit the contact page hero to enable newsletter signup</div>";
        }
    } else {
        echo "<div class='error'>❌ No hero section found for contact page</div>";
        echo "<div class='info'>💡 Create a hero section for the contact page in admin/hero-sections.php</div>";
    }
    
    // Test the getHeroSection function
    echo "<h2>📋 Step 5: Test getHeroSection Function</h2>";
    
    if (function_exists('getHeroSection')) {
        $hero_data = getHeroSection('contact');
        if ($hero_data) {
            echo "<div class='success'>✅ getHeroSection('contact') returned data</div>";
            echo "<pre>";
            echo "Newsletter Enabled: " . ($hero_data['show_newsletter_input'] ? 'YES' : 'NO') . "\n";
            if ($hero_data['show_newsletter_input']) {
                echo "Placeholder: {$hero_data['newsletter_placeholder']}\n";
                echo "Button Text: {$hero_data['newsletter_button_text']}\n";
                echo "Success Message: {$hero_data['newsletter_success_message']}\n";
            }
            echo "</pre>";
        } else {
            echo "<div class='error'>❌ getHeroSection('contact') returned null</div>";
        }
    } else {
        echo "<div class='error'>❌ getHeroSection function not found</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 Summary</h2>";
echo "<div class='info'>This script checks:</div>";
echo "<ul>";
echo "<li>✅ Hero sections table structure</li>";
echo "<li>✅ Newsletter field existence</li>";
echo "<li>✅ Current hero sections with newsletter settings</li>";
echo "<li>✅ Contact page hero section specifically</li>";
echo "<li>✅ getHeroSection function test</li>";
echo "</ul>";

echo "<h2>🔧 Next Steps</h2>";
echo "<div class='info'>If newsletter signup isn't working:</div>";
echo "<ol>";
echo "<li>Go to <strong>admin/hero-sections.php</strong></li>";
echo "<li>Edit the contact page hero section</li>";
echo "<li>Check <strong>Enable Email Input (Newsletter Signup)</strong></li>";
echo "<li>Fill in the newsletter fields</li>";
echo "<li>Save the changes</li>";
echo "<li>Test the contact page</li>";
echo "</ol>";
?>
