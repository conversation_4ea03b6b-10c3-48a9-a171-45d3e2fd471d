<?php
/**
 * Fix Hero Section Button Colors
 * This script will check and update hero section button colors to use the theme color
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>🎨 Hero Section Button Color Fix</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:10px;border-radius:4px;margin:5px 0;} 
.error{color:red;background:#f8d7da;padding:10px;border-radius:4px;margin:5px 0;} 
.info{color:blue;background:#d1ecf1;padding:10px;border-radius:4px;margin:5px 0;} 
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:5px 0;}
table{width:100%;border-collapse:collapse;margin:20px 0;background:white;}
th,td{padding:12px;border:1px solid #ddd;text-align:left;}
th{background:#f8f9fa;font-weight:600;}
.color-box{width:30px;height:20px;border:1px solid #ccc;display:inline-block;margin-right:8px;}
</style>";

try {
    $db = Database::getConnection();
    
    // Check current hero sections
    echo "<div class='info'>📋 Current Hero Sections Button Colors:</div>";
    
    $stmt = $db->query("SELECT page_name, button_bg_color, button_text_color, button_hover_bg_color FROM hero_sections ORDER BY page_name");
    $hero_sections = $stmt->fetchAll();
    
    if (empty($hero_sections)) {
        echo "<div class='warning'>⚠️ No hero sections found in database</div>";
    } else {
        echo "<table>";
        echo "<tr><th>Page</th><th>Button Background</th><th>Button Text</th><th>Button Hover</th><th>Action</th></tr>";
        
        foreach ($hero_sections as $hero) {
            $needs_update = false;
            $bg_color = $hero['button_bg_color'] ?? '';
            $text_color = $hero['button_text_color'] ?? '';
            $hover_color = $hero['button_hover_bg_color'] ?? '';
            
            // Check if colors need updating
            if ($bg_color !== ACCENT_COLOR) $needs_update = true;
            if ($text_color !== '#ffffff') $needs_update = true;
            if ($hover_color !== '#d35400') $needs_update = true;
            
            echo "<tr>";
            echo "<td><strong>{$hero['page_name']}</strong></td>";
            echo "<td><span class='color-box' style='background:{$bg_color}'></span>{$bg_color}</td>";
            echo "<td><span class='color-box' style='background:{$text_color}'></span>{$text_color}</td>";
            echo "<td><span class='color-box' style='background:{$hover_color}'></span>{$hover_color}</td>";
            echo "<td>" . ($needs_update ? "❌ Needs Update" : "✅ Correct") . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Show theme colors
    echo "<div class='info'>🎨 Theme Colors:</div>";
    echo "<table>";
    echo "<tr><th>Color Type</th><th>Value</th><th>Preview</th></tr>";
    echo "<tr><td>Accent Color (Button Background)</td><td>" . ACCENT_COLOR . "</td><td><span class='color-box' style='background:" . ACCENT_COLOR . "'></span></td></tr>";
    echo "<tr><td>Button Text Color</td><td>#ffffff</td><td><span class='color-box' style='background:#ffffff;border:2px solid #ccc;'></span></td></tr>";
    echo "<tr><td>Button Hover Color</td><td>#d35400</td><td><span class='color-box' style='background:#d35400'></span></td></tr>";
    echo "</table>";
    
    // Fix button colors
    if (isset($_POST['fix_colors'])) {
        echo "<div class='info'>🔧 Fixing hero section button colors...</div>";
        
        $update_sql = "
            UPDATE hero_sections SET 
                button_bg_color = ?,
                button_text_color = ?,
                button_hover_bg_color = ?
            WHERE button_bg_color != ? OR button_text_color != ? OR button_hover_bg_color != ?
        ";
        
        $stmt = $db->prepare($update_sql);
        $result = $stmt->execute([
            ACCENT_COLOR,     // Set button_bg_color
            '#ffffff',        // Set button_text_color  
            '#d35400',        // Set button_hover_bg_color
            ACCENT_COLOR,     // Where button_bg_color != 
            '#ffffff',        // Where button_text_color !=
            '#d35400'         // Where button_hover_bg_color !=
        ]);
        
        if ($result) {
            $affected_rows = $stmt->rowCount();
            echo "<div class='success'>✅ Updated $affected_rows hero sections with correct theme colors!</div>";
            echo "<div class='info'>🔄 <a href=''>Refresh page</a> to see updated colors</div>";
        } else {
            echo "<div class='error'>❌ Failed to update hero section colors</div>";
        }
    }
    
    // Show fix form
    echo "<div style='background:white;padding:20px;border-radius:8px;margin:20px 0;'>";
    echo "<h3>🛠️ Fix Button Colors</h3>";
    echo "<p>Click the button below to update all hero sections to use the correct theme colors:</p>";
    echo "<form method='POST'>";
    echo "<button type='submit' name='fix_colors' style='background:" . ACCENT_COLOR . ";color:white;padding:12px 24px;border:none;border-radius:4px;cursor:pointer;font-weight:600;'>Fix All Button Colors</button>";
    echo "</form>";
    echo "</div>";
    
    // Test specific pages
    echo "<div class='info'>🧪 Testing Specific Pages:</div>";
    $test_pages = ['home', 'contact', 'about', 'services', 'projects'];
    
    foreach ($test_pages as $page) {
        $hero = getHeroSection($page);
        if ($hero) {
            $bg_color = $hero['button_bg_color'] ?? 'Not set';
            $is_correct = ($bg_color === ACCENT_COLOR);
            $status = $is_correct ? "✅" : "❌";
            echo "<div class='" . ($is_correct ? 'success' : 'warning') . "'>$status $page: Button color = $bg_color</div>";
        } else {
            echo "<div class='info'>ℹ️ $page: No hero section found</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}
?>
