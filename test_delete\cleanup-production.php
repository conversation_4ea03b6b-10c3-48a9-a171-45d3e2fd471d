<?php
/**
 * Production Cleanup Script
 * Moves all test, debug, and development files to test_delete directory
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🧹 Production Cleanup - Moving Test/Debug Files</h2>\n";

// Files to move from root directory
$root_files_to_move = [
    // Debug files
    'debug_contact_form_issue.php',
    'debug_contact_success.php', 
    'debug_hero_logic.php',
    'debug_newsletter_issues.php',
    'debug_service_heroes.php',
    
    // Test files
    'test_admin_frontend_connection.php',
    'test_all_fixes.php',
    'test_complete_fix.php',
    'test_contact_form_alert.php',
    'test_duplicate_prevention.php',
    'test_footer_newsletter.php',
    'test_getheroheader.php',
    'test_hero_headers_debug.php',
    'test_newsletter_fixes.php',
    
    // Test HTML files
    'test_hero_cta_form.html',
    'test_hero_cta_newsletter.html',
    'test_hero_cta_newsletter.php',
    
    // Fix files (temporary)
    'fix_static_hero.php',
    'final_newsletter_test.php',
    
    // Development files
    'install.php',
    'check_records.php',
    
    // Package files
    'package.json',
    'playwright.config.js'
];

// Admin files to move (backup files)
$admin_files_to_move = [
    'admin/services-backup.php',
    'admin/sliders-backup.php',
    'admin/theme-demo.php'
];

// Directories to move entirely
$directories_to_move = [
    'tests'
];

$moved_count = 0;
$failed_count = 0;
$skipped_count = 0;

echo "<h3>📁 Moving Root Files</h3>\n";

foreach ($root_files_to_move as $file) {
    $source = __DIR__ . '/../' . $file;
    $destination = __DIR__ . '/' . $file;
    
    if (file_exists($source)) {
        if (rename($source, $destination)) {
            echo "<p style='color: green;'>✅ Moved: {$file}</p>\n";
            $moved_count++;
        } else {
            echo "<p style='color: red;'>❌ Failed to move: {$file}</p>\n";
            $failed_count++;
        }
    } else {
        echo "<p style='color: gray;'>ℹ️ Not found: {$file}</p>\n";
        $skipped_count++;
    }
}

echo "<h3>📁 Moving Admin Files</h3>\n";

foreach ($admin_files_to_move as $file) {
    $source = __DIR__ . '/../' . $file;
    $destination = __DIR__ . '/' . basename($file);
    
    if (file_exists($source)) {
        if (rename($source, $destination)) {
            echo "<p style='color: green;'>✅ Moved: {$file}</p>\n";
            $moved_count++;
        } else {
            echo "<p style='color: red;'>❌ Failed to move: {$file}</p>\n";
            $failed_count++;
        }
    } else {
        echo "<p style='color: gray;'>ℹ️ Not found: {$file}</p>\n";
        $skipped_count++;
    }
}

echo "<h3>📁 Moving Directories</h3>\n";

foreach ($directories_to_move as $dir) {
    $source = __DIR__ . '/../' . $dir;
    $destination = __DIR__ . '/' . $dir;
    
    if (is_dir($source)) {
        if (rename($source, $destination)) {
            echo "<p style='color: green;'>✅ Moved directory: {$dir}</p>\n";
            $moved_count++;
        } else {
            echo "<p style='color: red;'>❌ Failed to move directory: {$dir}</p>\n";
            $failed_count++;
        }
    } else {
        echo "<p style='color: gray;'>ℹ️ Directory not found: {$dir}</p>\n";
        $skipped_count++;
    }
}

echo "<h3>🔍 Scanning for Additional Test Files</h3>\n";

// Scan for any remaining files that look like test/debug files
$root_dir = __DIR__ . '/../';
$files = glob($root_dir . '*.php');

$additional_test_files = [];
foreach ($files as $file) {
    $filename = basename($file);
    
    // Skip if already moved or is a core file
    if (in_array($filename, $root_files_to_move)) continue;
    
    // Check if filename suggests it's a test/debug file
    if (preg_match('/^(test_|debug_|temp_|sample_|example_|dev_|staging_)/i', $filename) ||
        strpos($filename, 'test') !== false ||
        strpos($filename, 'debug') !== false ||
        strpos($filename, 'temp') !== false) {
        
        // Skip core production files
        if (!in_array($filename, [
            'index.php', 'about.php', 'services.php', 'projects.php', 
            'team.php', 'blog.php', 'contact.php', 'config.php',
            'news.php', 'news-detail.php', 'news-details.php',
            'project-details.php', 'project-details-new.php',
            'service-details.php', 'team-details.php', 'team-new.php',
            'newsletter-signup.php', 'thank-you.php', '404.php'
        ])) {
            $additional_test_files[] = $filename;
        }
    }
}

if (!empty($additional_test_files)) {
    echo "<p style='color: orange;'>⚠️ Found additional potential test files:</p>\n";
    foreach ($additional_test_files as $file) {
        $source = $root_dir . $file;
        $destination = __DIR__ . '/' . $file;
        
        if (rename($source, $destination)) {
            echo "<p style='color: green;'>✅ Moved additional file: {$file}</p>\n";
            $moved_count++;
        } else {
            echo "<p style='color: red;'>❌ Failed to move: {$file}</p>\n";
            $failed_count++;
        }
    }
} else {
    echo "<p style='color: green;'>✅ No additional test files found</p>\n";
}

echo "<h3>🧹 Cleaning Up Empty Directories</h3>\n";

// Remove empty directories that might be left behind
$empty_dirs = [];
$dirs_to_check = [
    $root_dir . 'temp',
    $root_dir . 'debug',
    $root_dir . 'test'
];

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir) && count(scandir($dir)) == 2) { // Only . and ..
        if (rmdir($dir)) {
            echo "<p style='color: green;'>✅ Removed empty directory: " . basename($dir) . "</p>\n";
        }
    }
}

echo "<h3>📋 Production Files Verification</h3>\n";

// Verify core production files are still in place
$core_files = [
    'index.php',
    'about.php', 
    'services.php',
    'projects.php',
    'team.php',
    'blog.php',
    'contact.php',
    'config.php',
    'thank-you.php',
    '404.php'
];

$missing_core = [];
foreach ($core_files as $file) {
    if (!file_exists($root_dir . $file)) {
        $missing_core[] = $file;
    }
}

if (empty($missing_core)) {
    echo "<p style='color: green;'>✅ All core production files are present</p>\n";
} else {
    echo "<p style='color: red;'>❌ Missing core files:</p>\n";
    foreach ($missing_core as $file) {
        echo "<p style='color: red;'>   - {$file}</p>\n";
    }
}

// Check admin files
$admin_core_files = [
    'admin/index.php',
    'admin/login.php',
    'admin/logout.php',
    'admin/contacts.php',
    'admin/projects.php',
    'admin/services.php',
    'admin/team.php',
    'admin/blog.php',
    'admin/users.php'
];

$missing_admin = [];
foreach ($admin_core_files as $file) {
    if (!file_exists($root_dir . $file)) {
        $missing_admin[] = $file;
    }
}

if (empty($missing_admin)) {
    echo "<p style='color: green;'>✅ All core admin files are present</p>\n";
} else {
    echo "<p style='color: red;'>❌ Missing admin files:</p>\n";
    foreach ($missing_admin as $file) {
        echo "<p style='color: red;'>   - {$file}</p>\n";
    }
}

echo "<h3>📊 Summary</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
echo "<p><strong>Files moved:</strong> {$moved_count}</p>\n";
echo "<p><strong>Files failed:</strong> {$failed_count}</p>\n";
echo "<p><strong>Files skipped:</strong> {$skipped_count}</p>\n";
echo "</div>\n";

if ($failed_count > 0) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<p style='color: #721c24;'><strong>⚠️ Some files failed to move.</strong> Check file permissions.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<p style='color: #155724;'><strong>🎉 Cleanup completed successfully!</strong></p>\n";
    echo "<p>Your production environment is now clean of test and debug files.</p>\n";
    echo "</div>\n";
}

echo "<h3>🚀 Next Steps</h3>\n";
echo "<ul>\n";
echo "<li>Test your website to ensure everything works correctly</li>\n";
echo "<li>Check admin functionality</li>\n";
echo "<li>Verify contact forms and other features</li>\n";
echo "<li>Review file permissions if needed</li>\n";
echo "</ul>\n";

echo "<p><a href='../index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>→ View Website</a></p>\n";
echo "<p><a href='../admin/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>→ Admin Login</a></p>\n";
?>
