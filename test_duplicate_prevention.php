<?php
/**
 * Test Contact Form Duplicate Prevention Only
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "Testing Contact Form Duplicate Prevention\n";
echo "========================================\n\n";

$test_email = 'duplicate-test-' . time() . '@example.com';

// First contact form submission
echo "1. First contact submission...\n";
$first_submission = handleContactForm([
    'name' => 'Test User',
    'email' => $test_email,
    'phone' => '************',
    'service' => 'Architectural Design',
    'message' => 'This is a test message for duplicate prevention.'
]);

echo "Result: " . ($first_submission['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $first_submission['message'] . "\n\n";

// Second contact form submission (should be blocked)
echo "2. Second contact submission (should be blocked)...\n";
$second_submission = handleContactForm([
    'name' => 'Test User Again',
    'email' => $test_email,
    'phone' => '************',
    'service' => 'Engineering Services',
    'message' => 'This is a duplicate submission that should be blocked.'
]);

echo "Result: " . ($second_submission['success'] ? 'SUCCESS' : 'BLOCKED') . "\n";
echo "Message: " . $second_submission['message'] . "\n\n";

if (!$second_submission['success'] && strpos($second_submission['message'], 'already submitted') !== false) {
    echo "✅ Duplicate prevention working correctly!\n";
} else {
    echo "❌ Duplicate prevention not working!\n";
}

// Verify database
echo "\n3. Verifying database entries...\n";
$db = Database::getConnection();
$contact_count = $db->prepare("SELECT COUNT(*) as count FROM contact_submissions WHERE email = ? AND is_newsletter_signup = 0");
$contact_count->execute([$test_email]);
$contact_result = $contact_count->fetch();

echo "Contact submissions for test email: " . $contact_result['count'] . "\n";
echo "Expected: 1 (only first submission should be saved)\n";

if ($contact_result['count'] == 1) {
    echo "✅ Database correctly shows only one submission!\n";
} else {
    echo "❌ Database shows incorrect number of submissions!\n";
}
?>
