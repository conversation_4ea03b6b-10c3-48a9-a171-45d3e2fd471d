<?php
/**
 * Simple Contact Form Test
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

// Handle contact form submission
$form_message = '';
$form_status = '';
if ($_POST && isset($_POST['submit_contact'])) {
    $result = handleContactForm($_POST);
    $form_message = $result['message'];
    $form_status = $result['success'] ? 'success' : 'error';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Contact Form Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        
        /* Original form message styles */
        .form-message {
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }
        
        .form-message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .form-message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* POPUP SUCCESS MESSAGE */
        .popup-success {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            animation: popupFadeIn 0.5s ease-out;
            min-width: 300px;
        }
        
        .popup-error {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            animation: popupFadeIn 0.5s ease-out;
            min-width: 300px;
        }
        
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            animation: overlayFadeIn 0.3s ease-out;
        }
        
        @keyframes popupFadeIn {
            from { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }
        
        @keyframes overlayFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea, .form-group select { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            font-size: 16px;
            box-sizing: border-box;
        }
        .submit-btn { 
            background: #007cba; 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 16px; 
            font-weight: 600;
        }
        .submit-btn:hover { background: #005a87; }
        
        .test-info { background: #e7f3ff; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #007cba; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 Simple Contact Form Test</h1>
        
        <div class="test-info">
            <strong>🎯 Testing Contact Form Success Message Visibility</strong><br>
            This test uses both the original CSS styles AND popup-style messages to ensure visibility.
        </div>
        
        <?php if ($form_message): ?>
            <!-- Original form message (might be hidden by CSS conflicts) -->
            <div class="form-message <?php echo $form_status; ?>">
                <?php echo htmlspecialchars($form_message); ?>
            </div>
            
            <!-- POPUP MESSAGE (impossible to miss) -->
            <div class="popup-overlay" id="popupOverlay"></div>
            <div class="popup-<?php echo $form_status; ?>" id="popupMessage">
                <div style="font-size: 24px; margin-bottom: 10px;">
                    <?php echo $form_status === 'success' ? '✅' : '❌'; ?>
                </div>
                <div><?php echo htmlspecialchars($form_message); ?></div>
                <div style="margin-top: 15px; font-size: 14px; opacity: 0.8;">
                    Click anywhere to close
                </div>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="" class="contact-form-inner">
            <div class="form-group">
                <label for="name">Full Name *</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone">
            </div>
            
            <div class="form-group">
                <label for="service">Service Interested In</label>
                <select id="service" name="service">
                    <option value="">Select a service...</option>
                    <option value="Architectural Design">Architectural Design</option>
                    <option value="Structural Engineering">Structural Engineering</option>
                    <option value="Construction Management">Construction Management</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" rows="5" required placeholder="Tell us about your project..."></textarea>
            </div>
            
            <button type="submit" name="submit_contact" class="submit-btn">
                Send Message
            </button>
        </form>
        
        <div class="debug-info">
            <strong>Debug Info:</strong><br>
            Form Message: <?php echo $form_message ? "'{$form_message}'" : 'None'; ?><br>
            Form Status: <?php echo $form_status ? $form_status : 'None'; ?><br>
            POST Data: <?php echo $_POST ? 'Present' : 'None'; ?><br>
            Submit Button: <?php echo isset($_POST['submit_contact']) ? 'Clicked' : 'Not clicked'; ?>
        </div>
    </div>
    
    <script>
    // Close popup when clicking anywhere
    document.addEventListener('DOMContentLoaded', function() {
        const overlay = document.getElementById('popupOverlay');
        const popup = document.getElementById('popupMessage');
        
        if (overlay && popup) {
            function closePopup() {
                overlay.style.display = 'none';
                popup.style.display = 'none';
            }
            
            overlay.addEventListener('click', closePopup);
            popup.addEventListener('click', closePopup);
            
            // Auto-close after 5 seconds
            setTimeout(closePopup, 5000);
        }
    });
    </script>
</body>
</html>
