<?php
/**
 * Comprehensive Debug for Hero CTA and Contact Form Issues
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>🔍 COMPREHENSIVE DEBUG ANALYSIS</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:10px;border-radius:4px;margin:5px 0;} 
.error{color:red;background:#f8d7da;padding:10px;border-radius:4px;margin:5px 0;} 
.info{color:blue;background:#d1ecf1;padding:10px;border-radius:4px;margin:5px 0;} 
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:5px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow-x:auto;max-height:300px;overflow-y:auto;}
.test-section{background:white;padding:20px;margin:15px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1);}
.code-block{background:#2d3748;color:#e2e8f0;padding:15px;border-radius:4px;font-family:monospace;margin:10px 0;}
</style>";

try {
    $db = Database::getConnection();
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // ISSUE 1: Hero CTA Success Message
    echo "<div class='test-section'>";
    echo "<h2>🎯 ISSUE 1: Hero CTA Success Message Not Showing</h2>";
    
    // Check hero sections with newsletter enabled
    $stmt = $db->prepare("SELECT * FROM hero_sections WHERE show_newsletter_input = 1 ORDER BY page_name");
    $stmt->execute();
    $hero_sections = $stmt->fetchAll();
    
    if ($hero_sections) {
        echo "<div class='info'>📊 Found " . count($hero_sections) . " hero sections with newsletter enabled:</div>";
        foreach ($hero_sections as $section) {
            echo "<div class='info'>";
            echo "📄 <strong>{$section['page_name']}</strong> ({$section['page_title']})<br>";
            echo "✅ Success Message: '{$section['newsletter_success_message']}'<br>";
            echo "🔧 Active: " . ($section['active'] ? 'YES' : 'NO');
            echo "</div>";
        }
    } else {
        echo "<div class='error'>❌ No hero sections have newsletter enabled</div>";
    }
    
    // Test hero CTA JavaScript structure
    echo "<h3>🔍 Hero CTA Template Analysis</h3>";
    $hero_cta_path = __DIR__ . '/../templates/hero-cta.php';
    if (file_exists($hero_cta_path)) {
        $hero_content = file_get_contents($hero_cta_path);
        
        // Check for critical elements
        $js_checks = [
            'heroNewsletterForm ID' => preg_match('/id=["\']heroNewsletterForm["\']/', $hero_content),
            'heroNewsletterSuccessMessage ID' => preg_match('/id=["\']heroNewsletterSuccessMessage["\']/', $hero_content),
            'success-text class' => preg_match('/class=["\'][^"\']*success-text[^"\']*["\']/', $hero_content),
            'AJAX fetch call' => preg_match('/fetch\(this\.action/', $hero_content),
            'successMessage.style.display' => preg_match('/successMessage\.style\.display\s*=\s*["\']block["\']/', $hero_content),
            'form.style.display = none' => preg_match('/this\.style\.display\s*=\s*["\']none["\']/', $hero_content)
        ];
        
        echo "<div class='info'>🔍 JavaScript Structure Check:</div>";
        foreach ($js_checks as $check => $found) {
            $status = $found ? '✅' : '❌';
            $color = $found ? 'success' : 'error';
            echo "<div class='$color'>$status $check</div>";
        }
        
        // Extract the JavaScript section
        if (preg_match('/<script>(.*?)<\/script>/s', $hero_content, $matches)) {
            echo "<h4>📝 JavaScript Code Extract:</h4>";
            echo "<div class='code-block'>" . htmlspecialchars(substr($matches[1], 0, 1000)) . "...</div>";
        }
    }
    echo "</div>";
    
    // ISSUE 2: Contact Form Success Message
    echo "<div class='test-section'>";
    echo "<h2>📝 ISSUE 2: Contact Form Success Message Not Showing</h2>";
    
    // Test contact form handling
    echo "<h3>🧪 Testing Contact Form Handler</h3>";
    
    $test_contact_data = [
        'name' => 'Debug Test User',
        'email' => 'debug-contact-' . time() . '@example.com',
        'phone' => '555-0123',
        'service' => 'Testing',
        'message' => 'This is a debug test message'
    ];
    
    echo "<div class='info'>🔄 Testing with email: {$test_contact_data['email']}</div>";
    
    $contact_result = handleContactForm($test_contact_data);
    
    if ($contact_result['success']) {
        echo "<div class='success'>✅ Contact form handler working correctly</div>";
        echo "<div class='info'>📝 Success message: '{$contact_result['message']}'</div>";
    } else {
        echo "<div class='error'>❌ Contact form handler failed: {$contact_result['message']}</div>";
    }
    
    // Check contact.php structure
    echo "<h3>🔍 Contact.php Analysis</h3>";
    $contact_path = __DIR__ . '/../contact.php';
    if (file_exists($contact_path)) {
        $contact_content = file_get_contents($contact_path);
        
        $contact_checks = [
            'form_message variable' => preg_match('/\$form_message\s*=/', $contact_content),
            'form_status variable' => preg_match('/\$form_status\s*=/', $contact_content),
            'handleContactForm call' => preg_match('/handleContactForm\(\$_POST\)/', $contact_content),
            'form_message display' => preg_match('/if\s*\(\$form_message\)/', $contact_content),
            'submit_contact check' => preg_match('/isset\(\$_POST\[["\']submit_contact["\']\]\)/', $contact_content)
        ];
        
        echo "<div class='info'>🔍 Contact Form Structure Check:</div>";
        foreach ($contact_checks as $check => $found) {
            $status = $found ? '✅' : '❌';
            $color = $found ? 'success' : 'error';
            echo "<div class='$color'>$status $check</div>";
        }
    }
    echo "</div>";
    
    // ISSUE 3: Newsletter Signup Handler
    echo "<div class='test-section'>";
    echo "<h2>📧 ISSUE 3: Newsletter Signup Handler Analysis</h2>";
    
    // Test newsletter signup directly
    echo "<h3>🧪 Testing Newsletter Signup Handler</h3>";
    
    // Simulate AJAX request
    $original_post = $_POST;
    $original_server = $_SERVER;
    
    $_POST = [
        'email' => 'debug-newsletter-' . time() . '@example.com',
        'source' => 'hero_cta',
        'page' => 'contact'
    ];
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
    
    ob_start();
    include __DIR__ . '/../newsletter-signup.php';
    $newsletter_response = ob_get_clean();
    
    // Restore original values
    $_POST = $original_post;
    $_SERVER = $original_server;
    
    echo "<div class='info'>📤 Newsletter signup raw response:</div>";
    echo "<pre>" . htmlspecialchars($newsletter_response) . "</pre>";
    
    $response_data = json_decode($newsletter_response, true);
    if ($response_data) {
        if ($response_data['success']) {
            echo "<div class='success'>✅ Newsletter signup working correctly</div>";
            echo "<div class='info'>📝 Success message: '{$response_data['message']}'</div>";
        } else {
            echo "<div class='error'>❌ Newsletter signup failed: {$response_data['message']}</div>";
        }
    } else {
        echo "<div class='error'>❌ Invalid JSON response from newsletter-signup.php</div>";
        echo "<div class='error'>Raw response: " . htmlspecialchars($newsletter_response) . "</div>";
    }
    echo "</div>";
    
    // ISSUE 4: Database Integrity
    echo "<div class='test-section'>";
    echo "<h2>🗄️ ISSUE 4: Database Integrity Check</h2>";
    
    // Check recent submissions
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM newsletter_subscribers WHERE source = 'hero_cta'");
    $stmt->execute();
    $hero_cta_count = $stmt->fetch()['count'];
    
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM contact_submissions WHERE is_newsletter_signup = 0");
    $stmt->execute();
    $contact_count = $stmt->fetch()['count'];
    
    echo "<div class='info'>📊 Database Statistics:</div>";
    echo "<div class='success'>📧 Hero CTA Newsletter Subscriptions: $hero_cta_count</div>";
    echo "<div class='success'>📝 Contact Form Submissions: $contact_count</div>";
    
    // Check for recent duplicates
    $stmt = $db->prepare("
        SELECT email, COUNT(*) as count 
        FROM contact_submissions 
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) 
        GROUP BY email 
        HAVING count > 1
    ");
    $stmt->execute();
    $duplicates = $stmt->fetchAll();
    
    if ($duplicates) {
        echo "<div class='warning'>⚠️ Recent duplicate submissions found:</div>";
        foreach ($duplicates as $dup) {
            echo "<div class='warning'>📧 {$dup['email']}: {$dup['count']} submissions</div>";
        }
    } else {
        echo "<div class='success'>✅ No recent duplicate submissions</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<div class='test-section'>";
echo "<h2>🎯 DIAGNOSIS & SOLUTIONS</h2>";
echo "<div class='info'>
<strong>Based on the analysis above, here are the likely issues and solutions:</strong><br><br>

<strong>1. Hero CTA Success Message Issue:</strong><br>
- Check browser console for JavaScript errors<br>
- Verify successMessage element exists in DOM<br>
- Ensure AJAX response is valid JSON<br>
- Check if CSS is hiding the success message<br><br>

<strong>2. Contact Form Success Message Issue:</strong><br>
- Verify form has name='submit_contact' on submit button<br>
- Check if duplicate submission blocking is preventing success<br>
- Ensure form_message variable is properly set<br>
- Check CSS styling for .form-message class<br><br>

<strong>3. Common Solutions:</strong><br>
- Clear browser cache and test again<br>
- Check browser developer tools for errors<br>
- Verify database connections are working<br>
- Test with different email addresses<br>
</div>";
echo "</div>";
?>
