<!DOCTYPE html>
<html>
<head>
    <title>Newsletter Signup Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: bold; color: #555; }
        input[type="email"], select { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; width: 100%; }
        button:hover { background: #005a87; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin-top: 15px; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin-top: 15px; border: 1px solid #f5c6cb; }
        .loading { opacity: 0.7; }
        .test-info { background: #e7f3ff; padding: 15px; border-radius: 4px; margin-bottom: 20px; border-left: 4px solid #007cba; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Newsletter Signup Test</h1>
        
        <div class="test-info">
            <strong>Test Information:</strong><br>
            This form tests the newsletter signup functionality with AJAX submission.<br>
            It will submit to: <code>/newsletter-signup.php</code>
        </div>
        
        <form id="testNewsletterForm" method="POST" action="/monolith-design/newsletter-signup.php">
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" required placeholder="Enter your email address">
            </div>
            
            <div class="form-group">
                <label for="source">Source:</label>
                <select id="source" name="source">
                    <option value="footer">Footer</option>
                    <option value="hero_cta">Hero CTA</option>
                    <option value="newsletter_hero">Newsletter Hero</option>
                    <option value="test">Test Form</option>
                </select>
            </div>
            
            <button type="submit" id="submitBtn">Subscribe to Newsletter</button>
            <input type="hidden" name="page" value="test_page">
        </form>
        
        <div id="responseMessage" style="display: none;"></div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
            <h3>Test Instructions:</h3>
            <ol>
                <li>Enter a valid email address</li>
                <li>Select a source from the dropdown</li>
                <li>Click "Subscribe to Newsletter"</li>
                <li>Check for success/error messages</li>
                <li>Try subscribing with the same email again to test duplicate handling</li>
            </ol>
        </div>
    </div>
    
    <script>
    document.getElementById('testNewsletterForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const responseDiv = document.getElementById('responseMessage');
        const submitBtn = document.getElementById('submitBtn');
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.textContent = 'Subscribing...';
        this.classList.add('loading');
        responseDiv.style.display = 'none';
        
        console.log('Submitting to:', this.action);
        console.log('Form data:', Object.fromEntries(formData));
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Raw response:', text);
            
            // Try to parse as JSON
            let data;
            try {
                data = JSON.parse(text);
            } catch (e) {
                throw new Error('Invalid JSON response: ' + text);
            }
            
            responseDiv.style.display = 'block';
            responseDiv.className = data.success ? 'success' : 'error';
            responseDiv.innerHTML = '<strong>' + (data.success ? 'Success!' : 'Error!') + '</strong><br>' + data.message;
            
            if (data.success) {
                this.reset();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            responseDiv.style.display = 'block';
            responseDiv.className = 'error';
            responseDiv.innerHTML = '<strong>Error!</strong><br>An error occurred: ' + error.message;
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.textContent = 'Subscribe to Newsletter';
            this.classList.remove('loading');
        });
    });
    </script>
</body>
</html>
