<?php
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Test the same logic as service-details.php
$service_slug = 'architectural-design'; // Test with a known service
$current_service = getServiceBySlugWithHero($service_slug);

if ($current_service) {
    echo "Service found: {$current_service['title']}\n";
    
    // Test the page name generation
    $hero_page_name = 'service-details-' . $current_service['slug'];
    echo "Generated hero page name: $hero_page_name\n";
    
    // Test if we can get the hero header
    $hero_header = getHeroHeader($hero_page_name);
    
    if ($hero_header) {
        echo "Hero header found:\n";
        echo "- Title: {$hero_header['page_title']}\n";
        echo "- Subtitle: {$hero_header['subtitle']}\n";
        echo "- Active: {$hero_header['active']}\n";
    } else {
        echo "No hero header found for page name: $hero_page_name\n";
    }
    
    // Also test what happens without the variable set
    $default_page_name = basename('service-details.php', '.php');
    echo "\nDefault page name would be: $default_page_name\n";
    $default_hero = getHeroHeader($default_page_name);
    if ($default_hero) {
        echo "Default hero header: {$default_hero['page_title']}\n";
    }
} else {
    echo "Service not found: $service_slug\n";
}
?>
