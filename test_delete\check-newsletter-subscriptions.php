<?php
/**
 * Check Newsletter Subscriptions
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>📧 NEWSLETTER SUBSCRIPTIONS CHECK</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:4px;}</style>";

try {
    $db = Database::getConnection();
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // Check recent newsletter subscriptions
    echo "<h2>📋 Recent Newsletter Subscriptions</h2>";
    
    $stmt = $db->prepare("SELECT * FROM newsletter_subscribers ORDER BY subscribed_at DESC LIMIT 10");
    $stmt->execute();
    $subscribers = $stmt->fetchAll();
    
    if ($subscribers) {
        echo "<div class='info'>📊 Last 10 Newsletter Subscriptions:</div>";
        echo "<pre>";
        foreach ($subscribers as $sub) {
            echo "📧 {$sub['email']}\n";
            echo "   📍 Source: {$sub['source']}\n";
            echo "   📄 Page: {$sub['page']}\n";
            echo "   📅 Date: {$sub['subscribed_at']}\n";
            echo "   ✅ Status: {$sub['status']}\n";
            echo "\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='info'>ℹ️ No newsletter subscriptions found</div>";
    }
    
    // Check for hero_cta subscriptions specifically
    echo "<h2>📋 Hero CTA Subscriptions</h2>";
    
    $stmt = $db->prepare("SELECT * FROM newsletter_subscribers WHERE source = 'hero_cta' ORDER BY subscribed_at DESC LIMIT 5");
    $stmt->execute();
    $hero_subs = $stmt->fetchAll();
    
    if ($hero_subs) {
        echo "<div class='success'>✅ Found Hero CTA subscriptions:</div>";
        echo "<pre>";
        foreach ($hero_subs as $sub) {
            echo "📧 {$sub['email']} - {$sub['subscribed_at']}\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='info'>ℹ️ No Hero CTA subscriptions found yet</div>";
    }
    
    // Check total counts by source
    echo "<h2>📋 Subscription Counts by Source</h2>";
    
    $stmt = $db->prepare("SELECT source, COUNT(*) as count FROM newsletter_subscribers GROUP BY source ORDER BY count DESC");
    $stmt->execute();
    $counts = $stmt->fetchAll();
    
    if ($counts) {
        echo "<div class='info'>📊 Subscriptions by Source:</div>";
        echo "<pre>";
        foreach ($counts as $count) {
            echo "📍 {$count['source']}: {$count['count']} subscriptions\n";
        }
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 Summary</h2>";
echo "<div class='info'>This shows all newsletter subscriptions to verify the hero CTA is working.</div>";
?>
