<?php
/**
 * Debug Hero CTA Success Message Issues
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>🔍 HERO CTA SUCCESS MESSAGE DEBUG</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:10px;border-radius:4px;margin:5px 0;} 
.error{color:red;background:#f8d7da;padding:10px;border-radius:4px;margin:5px 0;} 
.info{color:blue;background:#d1ecf1;padding:10px;border-radius:4px;margin:5px 0;} 
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:5px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow-x:auto;}
.test-section{background:white;padding:20px;margin:15px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1);}
</style>";

try {
    $db = Database::getConnection();
    echo "<div class='success'>✅ Database connection successful</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🎯 TEST 1: Hero Sections with Newsletter Enabled</h2>";
    
    $stmt = $db->prepare("
        SELECT page_name, page_title, show_newsletter_input, newsletter_success_message, active 
        FROM hero_sections 
        WHERE show_newsletter_input = 1 
        ORDER BY page_name
    ");
    $stmt->execute();
    $hero_sections = $stmt->fetchAll();
    
    if ($hero_sections) {
        echo "<div class='info'>📊 Found " . count($hero_sections) . " hero sections with newsletter enabled:</div>";
        echo "<pre>";
        foreach ($hero_sections as $section) {
            $status = $section['active'] ? 'ACTIVE' : 'INACTIVE';
            echo "📄 {$section['page_name']} ({$section['page_title']}) - $status\n";
            echo "   📧 Newsletter: " . ($section['show_newsletter_input'] ? 'ENABLED' : 'DISABLED') . "\n";
            echo "   ✅ Success Message: '{$section['newsletter_success_message']}'\n\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='error'>❌ No hero sections have newsletter enabled</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🧪 TEST 2: Simulate Hero CTA Newsletter Signup</h2>";
    
    // Simulate AJAX request to newsletter-signup.php
    $test_email = 'debug-hero-' . time() . '@example.com';
    $test_source = 'hero_cta';
    $test_page = 'contact';
    
    echo "<div class='info'>🔄 Testing with: $test_email (source: $test_source, page: $test_page)</div>";
    
    // Simulate the POST data
    $_POST = [
        'email' => $test_email,
        'source' => $test_source,
        'page' => $test_page
    ];
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
    
    // Capture the newsletter-signup.php output
    ob_start();
    include __DIR__ . '/../newsletter-signup.php';
    $response_output = ob_get_clean();
    
    echo "<div class='info'>📤 Newsletter signup response:</div>";
    echo "<pre>" . htmlspecialchars($response_output) . "</pre>";
    
    // Try to parse as JSON
    $response_data = json_decode($response_output, true);
    if ($response_data) {
        if ($response_data['success']) {
            echo "<div class='success'>✅ Newsletter signup successful!</div>";
            echo "<div class='info'>📝 Success message: '{$response_data['message']}'</div>";
        } else {
            echo "<div class='error'>❌ Newsletter signup failed: {$response_data['message']}</div>";
        }
    } else {
        echo "<div class='error'>❌ Invalid JSON response from newsletter-signup.php</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>📋 TEST 3: Check Recent Newsletter Subscriptions</h2>";
    
    $stmt = $db->prepare("
        SELECT email, source, page, subscribed_at 
        FROM newsletter_subscribers 
        WHERE source = 'hero_cta' 
        ORDER BY subscribed_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $recent_subs = $stmt->fetchAll();
    
    if ($recent_subs) {
        echo "<div class='success'>✅ Found " . count($recent_subs) . " recent hero CTA subscriptions:</div>";
        echo "<pre>";
        foreach ($recent_subs as $sub) {
            echo "📧 {$sub['email']} - {$sub['source']} - {$sub['page']} - {$sub['subscribed_at']}\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='warning'>⚠️ No recent hero CTA subscriptions found</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🔍 TEST 4: Check Contact Form Success Display</h2>";
    
    // Test contact form submission
    $test_contact_data = [
        'name' => 'Debug Test User',
        'email' => 'debug-contact-' . time() . '@example.com',
        'phone' => '555-0123',
        'service' => 'Testing',
        'message' => 'This is a debug test message'
    ];
    
    echo "<div class='info'>🔄 Testing contact form with: {$test_contact_data['email']}</div>";
    
    $contact_result = handleContactForm($test_contact_data);
    
    if ($contact_result['success']) {
        echo "<div class='success'>✅ Contact form submission successful!</div>";
        echo "<div class='info'>📝 Success message: '{$contact_result['message']}'</div>";
    } else {
        echo "<div class='error'>❌ Contact form submission failed: {$contact_result['message']}</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🔧 TEST 5: Check Template Files</h2>";
    
    // Check if hero-cta.php exists and has the right structure
    $hero_cta_path = __DIR__ . '/../templates/hero-cta.php';
    if (file_exists($hero_cta_path)) {
        echo "<div class='success'>✅ templates/hero-cta.php exists</div>";
        
        $hero_content = file_get_contents($hero_cta_path);
        
        // Check for key elements
        $checks = [
            'heroNewsletterForm' => strpos($hero_content, 'heroNewsletterForm') !== false,
            'heroNewsletterSuccessMessage' => strpos($hero_content, 'heroNewsletterSuccessMessage') !== false,
            'success-text' => strpos($hero_content, 'success-text') !== false,
            'newsletter-success-message' => strpos($hero_content, 'newsletter-success-message') !== false,
            'AJAX fetch' => strpos($hero_content, 'fetch(this.action') !== false
        ];
        
        echo "<div class='info'>🔍 Template structure check:</div>";
        echo "<pre>";
        foreach ($checks as $element => $found) {
            $status = $found ? '✅' : '❌';
            echo "$status $element: " . ($found ? 'FOUND' : 'MISSING') . "\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='error'>❌ templates/hero-cta.php not found</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<div class='test-section'>";
echo "<h2>🎯 SUMMARY & RECOMMENDATIONS</h2>";
echo "<div class='info'>
<strong>Common Issues & Solutions:</strong><br><br>
1. <strong>Success Message Not Showing:</strong><br>
   - Check if JavaScript console shows errors<br>
   - Verify AJAX response is valid JSON<br>
   - Ensure success message element exists in DOM<br><br>
   
2. <strong>Contact Form Not Showing Success:</strong><br>
   - Check if form_message variable is set<br>
   - Verify handleContactForm returns proper response<br>
   - Check for duplicate submission blocking<br><br>
   
3. <strong>Hero CTA Issues:</strong><br>
   - Verify hero section has newsletter enabled in admin<br>
   - Check if page name matches database page_name<br>
   - Ensure newsletter-signup.php returns proper JSON<br>
</div>";
echo "</div>";
?>
