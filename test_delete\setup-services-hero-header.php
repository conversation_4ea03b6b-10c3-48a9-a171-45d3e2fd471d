<?php
/**
 * Setup Services Page Hero Header
 * Creates a hero header entry for the services page
 */

define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

try {
    $db = Database::getConnection();
    
    // Check if services hero header already exists
    $stmt = $db->prepare("SELECT id FROM hero_headers WHERE page_name = 'services'");
    $stmt->execute();
    $existing = $stmt->fetch();
    
    if ($existing) {
        echo "ℹ️ Services hero header already exists, updating...\n";
        
        // Update existing hero header
        $stmt = $db->prepare("
            UPDATE hero_headers SET 
                page_title = 'Our Services',
                subtitle = 'Comprehensive Architectural & Construction Solutions',
                show_breadcrumbs = 1,
                background_type = 'image',
                background_image = 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1600&h=900&fit=crop',
                background_gradient = 'linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(230, 126, 34, 0.6) 100%)',
                background_color = '#1A1A1A',
                background_opacity = 0.7,
                height_type = 'medium',
                height_custom = 400,
                padding_top = '4rem',
                padding_bottom = '4rem',
                title_color = '#ffffff',
                subtitle_color = '#ffffff',
                breadcrumb_color = '#ffffff',
                show_cta_button = 0,
                active = 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE page_name = 'services'
        ");
        $stmt->execute();
        
        echo "✅ Services hero header updated successfully!\n";
    } else {
        echo "🆕 Creating new services hero header...\n";
        
        // Create new hero header
        $stmt = $db->prepare("
            INSERT INTO hero_headers (
                page_name, page_title, subtitle, show_breadcrumbs,
                background_type, background_image, background_gradient, background_color, background_opacity,
                height_type, height_custom, padding_top, padding_bottom,
                title_color, subtitle_color, breadcrumb_color,
                show_cta_button, cta_button_text, cta_button_link, cta_button_color, cta_button_text_color,
                active, created_at, updated_at
            ) VALUES (
                'services', 'Our Services', 'Comprehensive Architectural & Construction Solutions', 1,
                'image', 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1600&h=900&fit=crop', 
                'linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(230, 126, 34, 0.6) 100%)', 
                '#1A1A1A', 0.7,
                'medium', 400, '4rem', '4rem',
                '#ffffff', '#ffffff', '#ffffff',
                0, '', '', '#E67E22', '#ffffff',
                1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
        ");
        $stmt->execute();
        
        echo "✅ Services hero header created successfully!\n";
    }
    
    // Verify the hero header
    $stmt = $db->prepare("SELECT * FROM hero_headers WHERE page_name = 'services'");
    $stmt->execute();
    $hero = $stmt->fetch();
    
    if ($hero) {
        echo "\n📊 Services Hero Header Details:\n";
        echo "   - Page Name: " . $hero['page_name'] . "\n";
        echo "   - Title: " . $hero['page_title'] . "\n";
        echo "   - Subtitle: " . $hero['subtitle'] . "\n";
        echo "   - Background Type: " . $hero['background_type'] . "\n";
        echo "   - Background Image: " . ($hero['background_image'] ? 'Set' : 'None') . "\n";
        echo "   - Active: " . ($hero['active'] ? 'Yes' : 'No') . "\n";
    }
    
    echo "\n🎉 Services page is now connected to the dynamic hero header system!\n";
    echo "\nNext steps:\n";
    echo "1. Visit /services.php to see the dynamic hero\n";
    echo "2. Go to Admin → Hero Headers to customize the services page hero\n";
    echo "3. Upload a custom background image if desired\n";
    echo "4. Adjust title, subtitle, and styling as needed\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
