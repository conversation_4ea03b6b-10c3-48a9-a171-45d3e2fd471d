<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter CTA Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; }
        .test-form { border: 1px solid #ccc; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .newsletter-input-group { display: flex; gap: 10px; margin: 10px 0; }
        .newsletter-hero-input { flex: 1; padding: 10px; border: 1px solid #ccc; border-radius: 4px; }
        .newsletter-hero-button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .newsletter-success-message { display: none; padding: 15px; background: #d4edda; color: #155724; border-radius: 4px; margin: 10px 0; }
        .success-icon { display: inline-block; margin-right: 10px; }
        .result { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Newsletter Hero CTA Test</h1>
    
    <div class="test-form">
        <h3>Test Hero CTA Newsletter Form</h3>
        <p>This form simulates the hero CTA newsletter signup with AJAX submission.</p>
        
        <form id="heroNewsletterForm" method="POST" action="/monolith-design/newsletter-signup.php">
            <div class="newsletter-input-group">
                <input type="email" name="email" class="newsletter-hero-input" placeholder="Enter your email address" required>
                <button type="submit" class="newsletter-hero-button">Subscribe</button>
            </div>
            <div class="newsletter-success-message" id="heroNewsletterSuccessMessage">
                <div class="success-icon">✅</div>
                <div class="success-text">Thank you for subscribing!</div>
            </div>
            <input type="hidden" name="source" value="hero_cta">
            <input type="hidden" name="page" value="contact">
        </form>
    </div>
    
    <div class="result" id="testResult">
        Ready to test. Enter an email and click Subscribe.
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('heroNewsletterForm');
        const successMessage = document.getElementById('heroNewsletterSuccessMessage');
        const testResult = document.getElementById('testResult');

        if (form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = this.querySelector('input[type="email"]').value;
                const formData = new FormData(this);

                if (email) {
                    testResult.innerHTML = 'Submitting...';
                    
                    // Send AJAX request to newsletter signup
                    fetch(this.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        testResult.innerHTML = `<strong>Response:</strong><br>Success: ${data.success}<br>Message: ${data.message}`;
                        
                        if (data.success) {
                            // Update success message with database content or response message
                            const successText = successMessage.querySelector('.success-text');
                            if (successText) {
                                successText.textContent = data.message;
                            }
                            
                            // Hide form and show success message
                            this.style.display = 'none';
                            successMessage.style.display = 'block';

                            // Reset form after 5 seconds
                            setTimeout(() => {
                                this.style.display = 'block';
                                successMessage.style.display = 'none';
                                this.reset();
                                testResult.innerHTML = 'Form reset. Ready for another test.';
                            }, 5000);
                        } else {
                            testResult.innerHTML += '<br><strong style="color: red;">Error: ' + (data.message || 'Unknown error') + '</strong>';
                        }
                    })
                    .catch(error => {
                        console.error('Newsletter signup error:', error);
                        testResult.innerHTML = '<strong style="color: red;">Network Error:</strong> ' + error.message;
                    });
                }
            });
        }
    });
    </script>
</body>
</html>
