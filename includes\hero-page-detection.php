<?php
/**
 * Dynamic Page Detection for Hero Sections
 * Automatically detect new pages and manage hero section assignments
 */

if (!defined('MONOLITH_ACCESS')) {
    exit('Direct access denied');
}

/**
 * Scan for PHP pages in the site root and detect new pages
 */
function scanForNewPages() {
    $db = Database::getConnection();
    $site_root = dirname(__DIR__);
    $detected_pages = [];
    
    // Scan for PHP files in the root directory
    $php_files = glob($site_root . '/*.php');
    
    foreach ($php_files as $file) {
        $filename = basename($file);
        
        // Skip admin, includes, and system files
        if (in_array($filename, ['config.php', 'functions.php', 'database.php', '404.php', 'sitemap.php'])) {
            continue;
        }
        
        // Skip files that start with test_, debug_, or are in excluded directories
        if (preg_match('/^(test_|debug_|admin|includes|assets|templates)/', $filename)) {
            continue;
        }
        
        // Extract page name and title
        $page_name = str_replace('.php', '', $filename);
        if ($page_name === 'index') {
            $page_name = 'home';
        }
        
        // Try to extract page title from the file
        $page_title = extractPageTitle($file, $page_name);
        
        $detected_pages[] = [
            'page_name' => $page_name,
            'page_title' => $page_title,
            'file_path' => $filename
        ];
    }
    
    return $detected_pages;
}

/**
 * Extract page title from PHP file content
 */
function extractPageTitle($file_path, $page_name) {
    $content = file_get_contents($file_path);
    
    // Try to find title in various formats
    $title_patterns = [
        '/<title[^>]*>([^<]+)<\/title>/i',
        '/\$page_title\s*=\s*[\'"]([^\'"]+)[\'"]/i',
        '/define\s*\(\s*[\'"]PAGE_TITLE[\'"]\s*,\s*[\'"]([^\'"]+)[\'"]/i'
    ];
    
    foreach ($title_patterns as $pattern) {
        if (preg_match($pattern, $content, $matches)) {
            $title = trim($matches[1]);
            // Clean up title
            $title = str_replace(' - Monolith Design Co.', '', $title);
            return $title;
        }
    }
    
    // Fallback: generate title from page name
    return ucwords(str_replace(['-', '_'], ' ', $page_name));
}

/**
 * Update site_pages table with detected pages
 */
function updateDetectedPages() {
    $db = Database::getConnection();
    $detected_pages = scanForNewPages();
    $new_pages = [];
    
    foreach ($detected_pages as $page) {
        try {
            // Check if page already exists
            $stmt = $db->prepare("SELECT id, has_hero_section FROM site_pages WHERE page_name = ?");
            $stmt->execute([$page['page_name']]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                // Update existing page
                $stmt = $db->prepare("
                    UPDATE site_pages 
                    SET page_title = ?, file_path = ?, last_checked = NOW() 
                    WHERE page_name = ?
                ");
                $stmt->execute([$page['page_title'], $page['file_path'], $page['page_name']]);
            } else {
                // Insert new page
                $stmt = $db->prepare("
                    INSERT INTO site_pages (page_name, page_title, file_path, has_hero_section, auto_detected) 
                    VALUES (?, ?, ?, 0, 1)
                ");
                $stmt->execute([$page['page_name'], $page['page_title'], $page['file_path']]);
                $new_pages[] = $page;
            }
        } catch (Exception $e) {
            error_log("Error updating page detection: " . $e->getMessage());
        }
    }
    
    return $new_pages;
}

/**
 * Get pages without hero sections
 */
function getPagesWithoutHeroSections() {
    $db = Database::getConnection();
    
    // Pages that shouldn't have hero sections or already have hardcoded ones
    $excluded_pages = ['install', 'admin', 'blog', 'news-details', 'project-details', 'project-details-new'];
    $excluded_placeholders = implode(',', array_fill(0, count($excluded_pages), '?'));
    
    $stmt = $db->prepare("
        SELECT sp.* 
        FROM site_pages sp 
        LEFT JOIN hero_sections hs ON sp.page_name = hs.page_name 
        WHERE hs.id IS NULL 
        AND sp.page_name NOT IN ($excluded_placeholders)
        ORDER BY sp.page_title
    ");
    
    $stmt->execute($excluded_pages);
    return $stmt->fetchAll();
}

/**
 * Create hero section for a new page
 */
function createHeroSectionForPage($page_name, $page_title, $hero_data = []) {
    $db = Database::getConnection();
    
    // Default hero section data
    $defaults = [
        'caption' => 'Welcome',
        'title' => 'Ready to Get Started?',
        'description' => 'Discover what we can do for you with our professional services and expertise.',
        'button_text' => 'Learn More',
        'button_link' => 'contact',
        'background_type' => 'gradient',
        'background_gradient' => 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)',
        'height_type' => 'medium',
        'caption_color' => '#ffffff',
        'title_color' => '#ffffff',
        'description_color' => '#ffffff',
        'button_bg_color' => '#E67E22',
        'button_text_color' => '#ffffff',
        'button_hover_bg_color' => '#d35400',
        'background_color' => '#000000',
        'background_opacity' => 0.60,
        'is_dynamic' => 1,
        'active' => 1
    ];
    
    // Merge with provided data
    $hero_data = array_merge($defaults, $hero_data);
    
    try {
        $stmt = $db->prepare("
            INSERT INTO hero_sections (
                page_name, page_title, caption, title, description, 
                button_text, button_link, background_type, background_gradient,
                height_type, caption_color, title_color, description_color,
                button_bg_color, button_text_color, button_hover_bg_color,
                background_color, background_opacity, is_dynamic, active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $page_name, $page_title, $hero_data['caption'], $hero_data['title'], 
            $hero_data['description'], $hero_data['button_text'], $hero_data['button_link'],
            $hero_data['background_type'], $hero_data['background_gradient'],
            $hero_data['height_type'], $hero_data['caption_color'], $hero_data['title_color'],
            $hero_data['description_color'], $hero_data['button_bg_color'], 
            $hero_data['button_text_color'], $hero_data['button_hover_bg_color'],
            $hero_data['background_color'], $hero_data['background_opacity'],
            $hero_data['is_dynamic'], $hero_data['active']
        ]);
        
        if ($result) {
            // Update site_pages table
            $stmt = $db->prepare("UPDATE site_pages SET has_hero_section = 1 WHERE page_name = ?");
            $stmt->execute([$page_name]);
            return true;
        }
        
    } catch (Exception $e) {
        error_log("Error creating hero section: " . $e->getMessage());
        return false;
    }
    
    return false;
}

/**
 * Get hero section styling as CSS variables
 */
function getHeroSectionCSS($hero_section) {
    $css_vars = [
        '--hero-height' => getHeroHeight($hero_section['height_type'], $hero_section['height_custom']),
        '--hero-caption-color' => $hero_section['caption_color'],
        '--hero-title-color' => $hero_section['title_color'],
        '--hero-description-color' => $hero_section['description_color'],
        '--hero-button-bg' => $hero_section['button_bg_color'],
        '--hero-button-text' => $hero_section['button_text_color'],
        '--hero-button-hover-bg' => $hero_section['button_hover_bg_color'],
        '--hero-bg-color' => $hero_section['background_color'],
        '--hero-bg-opacity' => $hero_section['background_opacity']
    ];
    
    $css = '';
    foreach ($css_vars as $var => $value) {
        $css .= "$var: $value; ";
    }
    
    return $css;
}

/**
 * Convert height type to CSS value
 */
function getHeroHeight($height_type, $custom_height = null) {
    switch ($height_type) {
        case 'small':
            return '300px';
        case 'medium':
            return '400px';
        case 'large':
            return '600px';
        case 'custom':
            return $custom_height ? $custom_height . 'px' : '400px';
        default:
            return '400px';
    }
}
?>
