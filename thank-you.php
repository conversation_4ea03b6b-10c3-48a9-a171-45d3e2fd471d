<?php
/**
 * Thank You Page - Monolith Design
 * Uses proper header/footer includes for consistency
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get message type and content from URL parameters
$message_type = isset($_GET['type']) ? $_GET['type'] : 'contact';
$custom_message = isset($_GET['message']) ? urldecode($_GET['message']) : '';

// Set default messages based on type
$messages = [
    'contact' => [
        'title' => 'Thank You!',
        'message' => 'Thank you for your message. We will get back to you soon!',
        'icon' => '<svg width="48" height="48" fill="none" stroke="currentColor" stroke-width="2"><circle cx="24" cy="24" r="22" stroke="' . ACCENT_COLOR . '"/><path d="M14 24l6 6 12-12" stroke="' . ACCENT_COLOR . '" stroke-width="3" fill="none"/></svg>',
        'color' => ACCENT_COLOR
    ],
    'newsletter' => [
        'title' => 'Subscribed!',
        'message' => 'Thank you for subscribing to our newsletter!',
        'icon' => '<svg width="48" height="48" fill="none" stroke="currentColor" stroke-width="2"><rect x="6" y="12" width="36" height="24" rx="4" stroke="' . ACCENT_COLOR . '"/><polyline points="6,12 24,30 42,12" stroke="' . ACCENT_COLOR . '" stroke-width="3" fill="none"/></svg>',
        'color' => ACCENT_COLOR
    ],
    'error' => [
        'title' => 'Oops!',
        'message' => 'Something went wrong. Please try again.',
        'icon' => '<svg width="48" height="48" fill="none" stroke="currentColor" stroke-width="2"><circle cx="24" cy="24" r="22" stroke="#dc3545"/><line x1="16" y1="16" x2="32" y2="32" stroke="#dc3545" stroke-width="3"/><line x1="32" y1="16" x2="16" y2="32" stroke="#dc3545" stroke-width="3"/></svg>',
        'color' => '#dc3545'
    ]
];

$current_message = $messages[$message_type] ?? $messages['contact'];
if ($custom_message) {
    $current_message['message'] = $custom_message;
}

// Set page title for header
$page_title = $current_message['title'] . ' - ' . SITE_NAME;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="Thank you for contacting <?php echo SITE_NAME; ?>. We will get back to you soon.">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo themeUrl('images/favicon.ico'); ?>">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Main CSS -->
    <link rel="stylesheet" href="<?php echo themeUrl('css/arkify-style.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/work-section-clean.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/responsive.css'); ?>">

    <style>
        /* Modern Success Page Styles */
        body {
            background: #fff;
            color: var(--text-color, #333);
            font-family: 'Public Sans', 'Inter', Arial, sans-serif;
        }
        .success-section {
            width: 100%;
            min-height: 80vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 80px 20px 60px 20px;
            position: relative;
            background: #fff;
        }
        .success-graphic {
            width: 90px;
            height: 90px;
            margin-bottom: 2rem;
            animation: bounceIn 1.2s cubic-bezier(.68,-0.55,.27,1.55);
        }
        @keyframes bounceIn {
            0% { transform: scale(0.7); opacity: 0; }
            60% { transform: scale(1.15); opacity: 1; }
            80% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }
        .success-heading {
            font-size: 2.8rem;
            font-weight: 800;
            color: <?php echo ACCENT_COLOR; ?>;
            margin-bottom: 1.2rem;
            letter-spacing: -1px;
            text-align: center;
            line-height: 1.1;
            animation: fadeInUp 1.1s cubic-bezier(.68,-0.55,.27,1.55);
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(40px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .success-message {
            font-size: 1.25rem;
            color: #333;
            margin-bottom: 2.5rem;
            text-align: center;
            line-height: 1.6;
            animation: fadeInUp 1.3s cubic-bezier(.68,-0.55,.27,1.55);
        }
        .success-actions {
            display: flex;
            gap: 1.2rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }
        .success-btn {
            background: <?php echo ACCENT_COLOR; ?>;
            color: #fff;
            border: none;
            border-radius: 32px;
            padding: 1rem 2.2rem;
            font-size: 1.1rem;
            font-weight: 700;
            text-decoration: none;
            box-shadow: 0 4px 16px rgba(230,126,34,0.13);
            cursor: pointer;
            transition: background 0.2s, transform 0.2s;
            outline: none;
            position: relative;
        }
        .success-btn:hover, .success-btn:focus {
            background: #d35400;
            transform: translateY(-2px) scale(1.04);
        }
        .success-btn:active {
            background: #ba6a1a;
        }
        .success-btn.secondary {
            background: #fff;
            color: <?php echo ACCENT_COLOR; ?>;
            border: 2px solid <?php echo ACCENT_COLOR; ?>;
            box-shadow: none;
        }
        .success-btn.secondary:hover, .success-btn.secondary:focus {
            background: <?php echo ACCENT_COLOR; ?>;
            color: #fff;
        }
        .success-info {
            margin-top: 2.5rem;
            text-align: center;
            color: #888;
            font-size: 1rem;
        }
        @media (max-width: 600px) {
            .success-section {
                padding: 40px 8px 30px 8px;
            }
            .success-heading {
                font-size: 2rem;
            }
            .success-message {
                font-size: 1.05rem;
            }
            .success-btn {
                padding: 0.8rem 1.2rem;
                font-size: 1rem;
            }
        }

        /* Ensure footer has consistent styling */
        .modern-footer {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
            color: #ffffff !important;
        }

        /* Ensure footer logo uses white version */
        .footer-logo {
            filter: none !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Main Content -->
    <main>
        <section class="success-section">
            <div class="success-graphic">
                <!-- Animated celebratory SVG (confetti burst) -->
                <svg viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="45" cy="45" r="40" fill="#fff" stroke="<?php echo ACCENT_COLOR; ?>" stroke-width="6"/>
                    <g>
                        <rect x="43" y="10" width="4" height="18" rx="2" fill="<?php echo ACCENT_COLOR; ?>"/>
                        <rect x="43" y="62" width="4" height="18" rx="2" fill="<?php echo ACCENT_COLOR; ?>"/>
                        <rect x="10" y="43" width="18" height="4" rx="2" fill="<?php echo ACCENT_COLOR; ?>"/>
                        <rect x="62" y="43" width="18" height="4" rx="2" fill="<?php echo ACCENT_COLOR; ?>"/>
                        <rect x="25" y="25" width="4" height="12" rx="2" fill="<?php echo ACCENT_COLOR; ?>" transform="rotate(-45 27 31)"/>
                        <rect x="61" y="25" width="4" height="12" rx="2" fill="<?php echo ACCENT_COLOR; ?>" transform="rotate(45 63 31)"/>
                        <rect x="25" y="61" width="4" height="12" rx="2" fill="<?php echo ACCENT_COLOR; ?>" transform="rotate(45 27 67)"/>
                        <rect x="61" y="61" width="4" height="12" rx="2" fill="<?php echo ACCENT_COLOR; ?>" transform="rotate(-45 63 67)"/>
                    </g>
                </svg>
            </div>
            <div class="success-heading">
                <?php
                // Creative, celebratory messaging
                if ($message_type === 'contact') {
                    echo 'Message Sent!';
                } elseif ($message_type === 'newsletter') {
                    echo 'You&rsquo;re on the List!';
                } elseif ($message_type === 'error') {
                    echo 'Something Went Wrong';
                } else {
                    echo 'Success!';
                }
                ?>
            </div>
            <div class="success-message">
                <?php
                // Creative, engaging message
                if ($message_type === 'contact') {
                    echo 'We&rsquo;ve received your message and our team will be in touch soon.<br>Ready to start your next project? Explore our work or get in touch for a consultation.';
                } elseif ($message_type === 'newsletter') {
                    echo 'You&rsquo;ll now receive the latest updates, insights, and design inspiration straight to your inbox.';
                } elseif ($message_type === 'error') {
                    echo 'We hit a snag processing your request. Please try again or contact us directly.';
                } else {
                    echo htmlspecialchars($current_message['message']);
                }
                ?>
            </div>
            <div class="success-actions">
                <a href="<?php echo siteUrl(); ?>" class="success-btn">
                    Back to Home
                </a>
                <?php if ($message_type === 'contact'): ?>
                <a href="<?php echo siteUrl('work'); ?>" class="success-btn secondary">
                    View Our Work
                </a>
                <a href="<?php echo siteUrl('contact'); ?>" class="success-btn secondary">
                    Contact Again
                </a>
                <?php endif; ?>
            </div>
            <div class="success-info">
                <span>Monolith Design Co. &mdash; Engineering the Future of Structures</span>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>
</body>
</html>
