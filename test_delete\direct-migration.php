<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Direct Database Migration</h2>\n";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=monolith_design;charset=utf8', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Database connected successfully</p>\n";
    
    // Create admin_users table
    $sql = "CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) NOT NULL UNIQUE,
        role ENUM('super_admin', 'contact_admin') NOT NULL DEFAULT 'contact_admin',
        status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        created_by INT NULL,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ admin_users table created</p>\n";
    
    echo "<p style='color: blue;'>ℹ Skipping sessions and login attempts tables for now</p>\n";
    
    // Insert default users
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO admin_users (username, password, email, role, status) VALUES (?, ?, ?, ?, 'active')");
    
    $users = [
        ['admin', $adminPassword, '<EMAIL>', 'super_admin'],
        ['contact_admin', $adminPassword, '<EMAIL>', 'contact_admin'],
        ['support', $adminPassword, '<EMAIL>', 'contact_admin']
    ];
    
    foreach ($users as $user) {
        $stmt->execute($user);
        echo "<p style='color: green;'>✓ User '{$user[0]}' created</p>\n";
    }
    
    echo "<h3 style='color: green;'>Migration Complete!</h3>\n";
    echo "<p><strong>Login Credentials:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Super Admin: <code>admin</code> / <code>admin123</code></li>\n";
    echo "<li>Contact Admin: <code>contact_admin</code> / <code>admin123</code></li>\n";
    echo "<li>Support: <code>support</code> / <code>admin123</code></li>\n";
    echo "</ul>\n";
    
    echo "<p><a href='../admin/login.php'>→ Try Admin Login</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Error details: " . htmlspecialchars($e->getTraceAsString()) . "</p>\n";
}
?>
