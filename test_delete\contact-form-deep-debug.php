<?php
/**
 * DEEP CONTACT FORM DEBUG ANALYSIS
 * This script will systematically test every component of the contact form system
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>🔍 DEEP CONTACT FORM DEBUG ANALYSIS</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:10px;border-radius:4px;margin:5px 0;} 
.error{color:red;background:#f8d7da;padding:10px;border-radius:4px;margin:5px 0;} 
.info{color:blue;background:#d1ecf1;padding:10px;border-radius:4px;margin:5px 0;} 
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:5px 0;}
.debug-section{background:white;padding:20px;margin:15px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1);}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow-x:auto;font-size:12px;}
.test-form{background:#e9ecef;padding:15px;border-radius:4px;margin:10px 0;}
</style>";

// Test 1: Basic PHP Configuration
echo "<div class='debug-section'>";
echo "<h2>🔧 Test 1: PHP Configuration</h2>";
echo "<div class='info'>PHP Version: " . phpversion() . "</div>";
echo "<div class='info'>Error Reporting: " . (error_reporting() ? 'Enabled' : 'Disabled') . "</div>";
echo "<div class='info'>Display Errors: " . (ini_get('display_errors') ? 'On' : 'Off') . "</div>";
echo "<div class='info'>Log Errors: " . (ini_get('log_errors') ? 'On' : 'Off') . "</div>";
echo "<div class='info'>Error Log: " . ini_get('error_log') . "</div>";
echo "</div>";

// Test 2: Database Connection
echo "<div class='debug-section'>";
echo "<h2>🗄️ Test 2: Database Connection</h2>";
try {
    $db = Database::getConnection();
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // Test table structure
    $stmt = $db->prepare("DESCRIBE contact_submissions");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<div class='info'>📋 Table structure:</div>";
    echo "<pre>";
    foreach ($columns as $col) {
        echo "{$col['Field']} - {$col['Type']} - {$col['Null']} - {$col['Key']}\n";
    }
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 3: Form Submission Detection
echo "<div class='debug-section'>";
echo "<h2>📝 Test 3: Form Submission Detection</h2>";

echo "<div class='info'>🔍 Current Request Method: " . $_SERVER['REQUEST_METHOD'] . "</div>";
echo "<div class='info'>🔍 POST Data Available: " . (empty($_POST) ? 'No' : 'Yes') . "</div>";

if (!empty($_POST)) {
    echo "<div class='info'>📋 POST Data Received:</div>";
    echo "<pre>";
    foreach ($_POST as $key => $value) {
        echo htmlspecialchars($key) . " = " . htmlspecialchars($value) . "\n";
    }
    echo "</pre>";
    
    echo "<div class='info'>🔍 submit_contact present: " . (isset($_POST['submit_contact']) ? 'Yes' : 'No') . "</div>";
}

echo "</div>";

// Test 4: Simulate Form Processing
echo "<div class='debug-section'>";
echo "<h2>🧪 Test 4: Simulate Form Processing</h2>";

$test_data = [
    'name' => 'Debug Test User',
    'email' => 'debug-test-' . time() . '@example.com',
    'phone' => '555-0123',
    'service' => 'Testing',
    'message' => 'This is a debug test message.',
    'submit_contact' => '1'
];

echo "<div class='info'>🔄 Testing with simulated data:</div>";
echo "<pre>";
foreach ($test_data as $key => $value) {
    echo htmlspecialchars($key) . " = " . htmlspecialchars($value) . "\n";
}
echo "</pre>";

// Simulate the exact contact form processing logic
echo "<div class='info'>🔄 Running contact form logic simulation...</div>";

$name = trim($test_data['name'] ?? '');
$email = trim($test_data['email'] ?? '');
$phone = trim($test_data['phone'] ?? '');
$service = trim($test_data['service'] ?? '');
$message = trim($test_data['message'] ?? '');

echo "<div class='info'>📝 Extracted data:</div>";
echo "<pre>";
echo "name = '$name'\n";
echo "email = '$email'\n";
echo "phone = '$phone'\n";
echo "service = '$service'\n";
echo "message = '$message'\n";
echo "</pre>";

// Test validation
$errors = [];
if (empty($name)) $errors[] = 'Name is required';
if (empty($email)) $errors[] = 'Email is required';
if (empty($message)) $errors[] = 'Message is required';
if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'Please enter a valid email address';
}

if (empty($errors)) {
    echo "<div class='success'>✅ Validation passed</div>";
    
    // Test database insertion
    try {
        $db = Database::getConnection();
        $stmt = $db->prepare("
            INSERT INTO contact_submissions (name, email, phone, service, message, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        echo "<div class='info'>🔄 Attempting database insertion...</div>";
        
        if ($stmt->execute([$name, $email, $phone, $service, $message])) {
            echo "<div class='success'>✅ Database insertion successful</div>";
            echo "<div class='info'>📝 Insert ID: " . $db->lastInsertId() . "</div>";
            
            // Test redirect URL generation
            $success_message = 'Thank you for your message. We will get back to you soon!';
            $redirect_url = siteUrl('thank-you.php?type=contact&message=' . urlencode($success_message));
            echo "<div class='info'>🔗 Generated redirect URL:</div>";
            echo "<pre>" . htmlspecialchars($redirect_url) . "</pre>";
            
            echo "<div class='success'>✅ All components working correctly!</div>";
            
        } else {
            echo "<div class='error'>❌ Database insertion failed</div>";
            echo "<div class='error'>Error info: " . print_r($stmt->errorInfo(), true) . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Database exception: " . $e->getMessage() . "</div>";
    }
    
} else {
    echo "<div class='error'>❌ Validation failed:</div>";
    echo "<pre>" . implode("\n", $errors) . "</pre>";
}

echo "</div>";

// Test 5: Headers and Output
echo "<div class='debug-section'>";
echo "<h2>📤 Test 5: Headers and Output</h2>";

echo "<div class='info'>🔍 Headers sent: " . (headers_sent() ? 'Yes' : 'No') . "</div>";

if (headers_sent($file, $line)) {
    echo "<div class='warning'>⚠️ Headers already sent at $file:$line</div>";
} else {
    echo "<div class='success'>✅ Headers not sent yet - redirects should work</div>";
}

echo "<div class='info'>🔍 Output buffering: " . (ob_get_level() ? 'Active (level ' . ob_get_level() . ')' : 'Inactive') . "</div>";

echo "</div>";

// Test 6: File Permissions and Includes
echo "<div class='debug-section'>";
echo "<h2>📁 Test 6: File Permissions and Includes</h2>";

$files_to_check = [
    'contact.php',
    'config.php',
    'includes/functions.php',
    'thank-you.php'
];

foreach ($files_to_check as $file) {
    $full_path = __DIR__ . '/../' . $file;
    if (file_exists($full_path)) {
        $perms = substr(sprintf('%o', fileperms($full_path)), -4);
        echo "<div class='success'>✅ $file exists (permissions: $perms)</div>";
    } else {
        echo "<div class='error'>❌ $file missing</div>";
    }
}

echo "</div>";

// Test 7: Live Form Test
echo "<div class='debug-section'>";
echo "<h2>🎯 Test 7: Live Form Test</h2>";

echo "<div class='info'>Use this form to test the actual contact form processing:</div>";

echo "<div class='test-form'>";
echo "<form method='POST' action=''>";
echo "<p><strong>Name:</strong> <input type='text' name='name' value='Live Test User' required></p>";
echo "<p><strong>Email:</strong> <input type='email' name='email' value='live-test-" . time() . "@example.com' required></p>";
echo "<p><strong>Phone:</strong> <input type='tel' name='phone' value='555-9999'></p>";
echo "<p><strong>Service:</strong> <input type='text' name='service' value='Live Testing'></p>";
echo "<p><strong>Message:</strong><br><textarea name='message' required>This is a live test of the contact form debug system.</textarea></p>";
echo "<p><button type='submit' name='submit_contact' style='background:#007cba;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;'>🚀 Test Contact Form</button></p>";
echo "</form>";
echo "</div>";

// Process the live form if submitted
if ($_POST && isset($_POST['submit_contact'])) {
    echo "<div class='info'>🔄 Processing live form submission...</div>";
    
    // Get form data
    $live_name = trim($_POST['name'] ?? '');
    $live_email = trim($_POST['email'] ?? '');
    $live_phone = trim($_POST['phone'] ?? '');
    $live_service = trim($_POST['service'] ?? '');
    $live_message = trim($_POST['message'] ?? '');
    
    echo "<div class='info'>📝 Live form data:</div>";
    echo "<pre>";
    echo "name = '$live_name'\n";
    echo "email = '$live_email'\n";
    echo "phone = '$live_phone'\n";
    echo "service = '$live_service'\n";
    echo "message = '$live_message'\n";
    echo "</pre>";
    
    // Simple validation
    $live_errors = [];
    if (empty($live_name)) $live_errors[] = 'Name is required';
    if (empty($live_email)) $live_errors[] = 'Email is required';
    if (empty($live_message)) $live_errors[] = 'Message is required';
    if (!empty($live_email) && !filter_var($live_email, FILTER_VALIDATE_EMAIL)) {
        $live_errors[] = 'Please enter a valid email address';
    }
    
    if (empty($live_errors)) {
        try {
            // Direct database insertion
            $db = Database::getConnection();
            $stmt = $db->prepare("
                INSERT INTO contact_submissions (name, email, phone, service, message, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            if ($stmt->execute([$live_name, $live_email, $live_phone, $live_service, $live_message])) {
                echo "<div class='success'>🎉 LIVE TEST SUCCESSFUL!</div>";
                echo "<div class='success'>✅ Form data processed correctly</div>";
                echo "<div class='success'>✅ Database insertion successful</div>";
                echo "<div class='info'>📝 Insert ID: " . $db->lastInsertId() . "</div>";
                
                // Show what the redirect would be
                $success_message = 'Thank you for your message. We will get back to you soon!';
                $redirect_url = siteUrl('thank-you.php?type=contact&message=' . urlencode($success_message));
                echo "<div class='info'>🔗 Would redirect to:</div>";
                echo "<pre>" . htmlspecialchars($redirect_url) . "</pre>";
                
                echo "<div class='warning'>⚠️ Redirect disabled in debug mode</div>";
                
            } else {
                echo "<div class='error'>❌ Database insertion failed</div>";
                echo "<div class='error'>Error info: " . print_r($stmt->errorInfo(), true) . "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Exception during live test: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='error'>❌ Live form validation failed:</div>";
        echo "<pre>" . implode("\n", $live_errors) . "</pre>";
    }
}

echo "</div>";

// Test 8: Diagnosis and Recommendations
echo "<div class='debug-section'>";
echo "<h2>🎯 DIAGNOSIS AND RECOMMENDATIONS</h2>";

echo "<div class='info'>";
echo "<strong>Based on the tests above, here are the most likely issues:</strong><br><br>";

echo "<strong>1. Headers Already Sent:</strong><br>";
echo "If headers are already sent, PHP redirects won't work. Check for any output before the redirect.<br><br>";

echo "<strong>2. JavaScript Form Handling:</strong><br>";
echo "JavaScript might be intercepting the form submission and preventing the POST request.<br><br>";

echo "<strong>3. Form Action URL:</strong><br>";
echo "The form action might not be pointing to the correct URL.<br><br>";

echo "<strong>4. PHP Error Suppression:</strong><br>";
echo "PHP errors might be suppressed and not visible.<br><br>";

echo "<strong>NEXT STEPS:</strong><br>";
echo "1. Check if the live form test above works<br>";
echo "2. If it works, the issue is with the main contact.php form<br>";
echo "3. If it doesn't work, check database and PHP configuration<br>";
echo "4. Look for JavaScript console errors in browser<br>";
echo "5. Check server error logs for PHP errors<br>";
echo "</div>";

echo "</div>";
?>
