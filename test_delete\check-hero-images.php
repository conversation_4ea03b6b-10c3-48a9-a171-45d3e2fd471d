<?php
/**
 * Check Hero Headers Images
 */

define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

try {
    $db = Database::getConnection();
    
    echo "<h2>Hero Headers Background Images</h2>\n";
    
    $stmt = $db->query("SELECT page_name, page_title, background_type, background_image FROM hero_headers ORDER BY page_name");
    $headers = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>\n";
    echo "<tr style='background: #f0f0f0;'><th>Page</th><th>Title</th><th>Background Type</th><th>Background Image</th><th>Absolute URL</th><th>File Exists</th></tr>\n";
    
    foreach ($headers as $header) {
        $absolute_url = ensureAbsoluteUrl($header['background_image']);
        $file_path = dirname(__DIR__) . '/' . $header['background_image'];
        $file_exists = file_exists($file_path) ? '✅ Yes' : '❌ No';
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$header['page_name']}</td>";
        echo "<td style='padding: 8px;'>{$header['page_title']}</td>";
        echo "<td style='padding: 8px;'>{$header['background_type']}</td>";
        echo "<td style='padding: 8px;'>{$header['background_image']}</td>";
        echo "<td style='padding: 8px;'><a href='{$absolute_url}' target='_blank'>{$absolute_url}</a></td>";
        echo "<td style='padding: 8px;'>{$file_exists}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
}
?>
