/**
 * Monolith Design Co. - Main JavaScript
 * Interactive functionality and animations
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all components
    initHeader();
    initSlider();
    initMobileMenu();
    initTestimonials();
    initAnimations();
    initSmoothScrolling();
    initFAQ();
    
    // Header scroll effect
    function initHeader() {
        const header = document.getElementById('header');
        if (!header) return;
        
        let lastScroll = 0;
        
        window.addEventListener('scroll', function() {
            const currentScroll = window.pageYOffset;
            
            // Add scrolled class for styling
            if (currentScroll > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            
            // Hide/show header on scroll (optional)
            if (currentScroll > lastScroll && currentScroll > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }
            
            lastScroll = currentScroll;
        });
    }
    
    // Hero Slider functionality
    function initSlider() {
        const sliderContainer = document.querySelector('.slider-container');
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.dot');
        const prevBtn = document.querySelector('.slider-prev');
        const nextBtn = document.querySelector('.slider-next');
        
        if (!slides || slides.length === 0) return;
        
        let currentSlide = 0;
        let slideInterval;
        
        // Auto-play settings
        const autoPlayInterval = 5000; // 5 seconds
        
        function showSlide(index) {
            // Remove active class from all slides and dots
            slides.forEach(slide => slide.classList.remove('active'));
            if (dots.length > 0) {
                dots.forEach(dot => dot.classList.remove('active'));
            }
            
            // Add active class to current slide and dot
            if (slides[index]) {
                slides[index].classList.add('active');
            }
            if (dots[index]) {
                dots[index].classList.add('active');
            }
            
            currentSlide = index;
        }
        
        function nextSlide() {
            const next = (currentSlide + 1) % slides.length;
            showSlide(next);
        }
        
        function prevSlide() {
            const prev = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(prev);
        }
        
        function startAutoPlay() {
            slideInterval = setInterval(nextSlide, autoPlayInterval);
        }
        
        function stopAutoPlay() {
            clearInterval(slideInterval);
        }
        
        // Event listeners
        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                nextSlide();
                stopAutoPlay();
                setTimeout(startAutoPlay, 2000); // Restart after 2 seconds
            });
        }
        
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                prevSlide();
                stopAutoPlay();
                setTimeout(startAutoPlay, 2000); // Restart after 2 seconds
            });
        }
        
        // Dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', function() {
                showSlide(index);
                stopAutoPlay();
                setTimeout(startAutoPlay, 2000); // Restart after 2 seconds
            });
        });
        
        // Pause on hover
        if (sliderContainer) {
            sliderContainer.addEventListener('mouseenter', stopAutoPlay);
            sliderContainer.addEventListener('mouseleave', startAutoPlay);
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                prevSlide();
            } else if (e.key === 'ArrowRight') {
                nextSlide();
            }
        });
        
        // Touch/swipe support for mobile
        let touchStartX = 0;
        let touchEndX = 0;
        
        if (sliderContainer) {
            sliderContainer.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            });
            
            sliderContainer.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });
        }
        
        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;
            
            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    nextSlide(); // Swipe left - next slide
                } else {
                    prevSlide(); // Swipe right - previous slide
                }
                stopAutoPlay();
                setTimeout(startAutoPlay, 2000);
            }
        }
        
        // Start autoplay if multiple slides
        if (slides.length > 1) {
            startAutoPlay();
        }
    }
    
    // Mobile menu functionality
    function initMobileMenu() {
        const mobileToggle = document.getElementById('mobile-toggle');
        const mobileNav = document.getElementById('mobile-nav');
        const body = document.body;
        
        if (!mobileToggle || !mobileNav) return;
        
        let isMenuOpen = false;
        
        mobileToggle.addEventListener('click', function() {
            isMenuOpen = !isMenuOpen;
            
            if (isMenuOpen) {
                mobileNav.style.display = 'block';
                body.style.overflow = 'hidden';
                mobileToggle.classList.add('active');
                
                // Animate hamburger lines
                const lines = mobileToggle.querySelectorAll('.hamburger-line');
                if (lines.length >= 3) {
                    lines[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                    lines[1].style.opacity = '0';
                    lines[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
                }
            } else {
                mobileNav.style.display = 'none';
                body.style.overflow = '';
                mobileToggle.classList.remove('active');
                
                // Reset hamburger lines
                const lines = mobileToggle.querySelectorAll('.hamburger-line');
                if (lines.length >= 3) {
                    lines[0].style.transform = '';
                    lines[1].style.opacity = '';
                    lines[2].style.transform = '';
                }
            }
        });
        
        // Close menu when clicking on links
        const mobileLinks = mobileNav.querySelectorAll('.mobile-nav-link');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (!link.parentElement.classList.contains('has-submenu')) {
                    // Only close if it's not a submenu parent
                    mobileToggle.click();
                }
            });
        });
        
        // Handle mobile submenu toggles
        const submenuToggles = mobileNav.querySelectorAll('.submenu-toggle');
        submenuToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const submenu = toggle.parentElement.nextElementSibling;
                const isOpen = submenu.style.display === 'block';
                
                if (isOpen) {
                    submenu.style.display = 'none';
                    toggle.textContent = '+';
                } else {
                    submenu.style.display = 'block';
                    toggle.textContent = '−';
                }
            });
        });
        
        // Close menu on window resize to desktop size
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768 && isMenuOpen) {
                mobileToggle.click();
            }
        });
    }
    
    // Testimonials slider
    function initTestimonials() {
        const testimonialSlides = document.querySelectorAll('.testimonial-slide');
        
        if (!testimonialSlides || testimonialSlides.length <= 1) return;
        
        let currentTestimonial = 0;
        const testimonialInterval = 8000; // 8 seconds
        
        function showTestimonial(index) {
            testimonialSlides.forEach(slide => slide.classList.remove('active'));
            if (testimonialSlides[index]) {
                testimonialSlides[index].classList.add('active');
            }
            currentTestimonial = index;
        }
        
        function nextTestimonial() {
            const next = (currentTestimonial + 1) % testimonialSlides.length;
            showTestimonial(next);
        }
        
        // Auto-rotate testimonials
        setInterval(nextTestimonial, testimonialInterval);
    }
    
    // Scroll animations
    function initAnimations() {
        const animatedElements = document.querySelectorAll('.loading');
        
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('loaded');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });
            
            animatedElements.forEach(element => {
                observer.observe(element);
            });
        } else {
            // Fallback for browsers without IntersectionObserver
            animatedElements.forEach(element => {
                element.classList.add('loaded');
            });
        }
        
        // Add loading class to elements that should animate
        const elementsToAnimate = document.querySelectorAll('.service-card, .project-card, .value-item');
        elementsToAnimate.forEach(element => {
            element.classList.add('loading');
        });
    }
    
    // Smooth scrolling for anchor links
    function initSmoothScrolling() {
        const anchors = document.querySelectorAll('a[href^="#"]');
        
        anchors.forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    e.preventDefault();
                    
                    const headerHeight = document.getElementById('header').offsetHeight;
                    const targetPosition = targetElement.offsetTop - headerHeight;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }
    
    // Utility functions
    
    // Debounce function for performance
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Throttle function for scroll events
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }
    
    // Form validation (for contact forms)
    function validateForm(form) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('error');
            } else {
                field.classList.remove('error');
            }
        });
        
        // Email validation
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (field.value && !emailRegex.test(field.value)) {
                isValid = false;
                field.classList.add('error');
            } else if (field.value) {
                field.classList.remove('error');
            }
        });
        
        return isValid;
    }
    
    // Add error styles
    const style = document.createElement('style');
    style.textContent = `
        .error {
            border-color: #e74c3c !important;
            box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
        }
        
        .error:focus {
            border-color: #e74c3c !important;
            box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.3) !important;
        }
    `;
    document.head.appendChild(style);
    
    // Handle contact forms - FIXED: Target actual form elements, not container divs
    const contactFormElements = document.querySelectorAll('.contact-form-inner, form[method="POST"]');
    contactFormElements.forEach(form => {
        // Only add validation to forms that don't already have it
        if (!form.hasAttribute('data-validation-added')) {
            form.setAttribute('data-validation-added', 'true');

            form.addEventListener('submit', function(e) {
                if (!validateForm(form)) {
                    e.preventDefault();

                    // Show error message
                    let errorMsg = form.querySelector('.error-message');
                    if (!errorMsg) {
                        errorMsg = document.createElement('div');
                        errorMsg.className = 'error-message';
                        errorMsg.style.cssText = 'color: #e74c3c; margin-top: 1rem; font-size: 0.875rem;';
                        form.appendChild(errorMsg);
                    }
                    errorMsg.textContent = 'Please fill in all required fields correctly.';
                } else {
                    // Remove any existing error messages when form is valid
                    const errorMsg = form.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                }
            });
        }
    });
    
    // Disabled lazy loading for better performance in Featured Work and Articles sections
    // Images now load immediately via arkify-main.js
    
    // Console log for development
    console.log('Monolith Design Co. - Website Loaded Successfully');
    console.log('🏗️ Engineering the Future of Structures');
});

// Additional utility functions available globally

// Format phone numbers
function formatPhoneNumber(phoneNumber) {
    const cleaned = phoneNumber.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
        return '(' + match[1] + ') ' + match[2] + '-' + match[3];
    }
    return phoneNumber;
}

// Copy to clipboard functionality
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(function() {
            console.log('Text copied to clipboard');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    }
}

// Scroll to top function
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Check if element is in viewport
function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// FAQ Accordion functionality
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', function() {
            // Close all other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });

            // Toggle current item
            item.classList.toggle('active');
        });
    });
}
