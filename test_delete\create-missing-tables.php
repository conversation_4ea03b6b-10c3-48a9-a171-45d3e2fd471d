<?php
/**
 * Create Missing Admin Tables
 * Creates admin_sessions and admin_login_attempts tables
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Creating Missing Admin Tables</h2>\n";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=monolith_design;charset=utf8', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Database connected successfully</p>\n";
    
    // Create admin_sessions table
    $sql = "CREATE TABLE IF NOT EXISTS admin_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_id VARCHAR(128) NOT NULL UNIQUE,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT,
        created_at DATETIME NOT NULL,
        expires_at DATETIME NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_user_id (user_id),
        INDEX idx_session_id (session_id),
        INDEX idx_expires_at (expires_at),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ admin_sessions table created</p>\n";
    
    // Create admin_login_attempts table
    $sql = "CREATE TABLE IF NOT EXISTS admin_login_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50),
        ip_address VARCHAR(45) NOT NULL,
        attempted_at DATETIME NOT NULL,
        success BOOLEAN DEFAULT FALSE,
        user_agent TEXT,
        failure_reason VARCHAR(255),
        INDEX idx_username (username),
        INDEX idx_ip_address (ip_address),
        INDEX idx_attempted_at (attempted_at),
        INDEX idx_success (success)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ admin_login_attempts table created</p>\n";
    
    // Add foreign key constraint for admin_sessions
    try {
        $sql = "ALTER TABLE admin_sessions 
                ADD CONSTRAINT fk_admin_sessions_user_id 
                FOREIGN KEY (user_id) REFERENCES admin_users(id) 
                ON DELETE CASCADE";
        $pdo->exec($sql);
        echo "<p style='color: green;'>✓ Foreign key constraint added to admin_sessions</p>\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p style='color: blue;'>ℹ Foreign key constraint already exists</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ Could not add foreign key: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
    }
    
    // Verify tables exist
    echo "<h3>Verification</h3>\n";
    $tables = ['admin_users', 'admin_sessions', 'admin_login_attempts'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ {$table} table exists</p>\n";
            
            // Show table structure
            $stmt = $pdo->query("DESCRIBE {$table}");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<details><summary>View {$table} structure</summary>\n";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
            echo "</details>\n";
        } else {
            echo "<p style='color: red;'>✗ {$table} table missing</p>\n";
        }
    }
    
    echo "<h3 style='color: green;'>✅ All Tables Created Successfully!</h3>\n";
    echo "<p><strong>Next Steps:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>✅ Database tables are now complete</li>\n";
    echo "<li>✅ Session tracking will now work properly</li>\n";
    echo "<li>✅ Login attempt logging will now work properly</li>\n";
    echo "<li>⚠️ Perform security audit of the codebase</li>\n";
    echo "<li>⚠️ Change default passwords</li>\n";
    echo "</ul>\n";
    
    echo "<p><a href='../admin/login.php'>→ Test Admin Login</a></p>\n";
    echo "<p><a href='test-admin-user-system.php'>→ Run System Test</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Error details: " . htmlspecialchars($e->getTraceAsString()) . "</p>\n";
}
?>
