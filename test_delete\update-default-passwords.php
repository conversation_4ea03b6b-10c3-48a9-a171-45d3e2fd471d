<?php
/**
 * Update Default Passwords - Security Fix
 * Changes all default passwords to secure ones
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔒 Security Fix: Update Default Passwords</h2>\n";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=monolith_design;charset=utf8', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Database connected successfully</p>\n";
    
    // Generate secure passwords
    $secure_passwords = [
        'admin' => 'MonolithAdmin2025!',
        'contact_admin' => 'ContactAdmin2025!',
        'support' => 'SupportAdmin2025!',
        'test_user' => 'TestUser2025!'
    ];
    
    echo "<h3>Updating Default Passwords</h3>\n";
    
    $stmt = $pdo->prepare("UPDATE admin_users SET password = ? WHERE username = ?");
    
    foreach ($secure_passwords as $username => $password) {
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        if ($stmt->execute([$hashed_password, $username])) {
            echo "<p style='color: green;'>✓ Updated password for user: <strong>{$username}</strong></p>\n";
        } else {
            echo "<p style='color: red;'>✗ Failed to update password for user: {$username}</p>\n";
        }
    }
    
    echo "<h3 style='color: green;'>✅ Password Update Complete!</h3>\n";
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h4>🔑 New Secure Login Credentials</h4>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr style='background: #e9ecef;'><th style='padding: 10px;'>Username</th><th style='padding: 10px;'>New Password</th><th style='padding: 10px;'>Role</th></tr>\n";
    
    foreach ($secure_passwords as $username => $password) {
        // Get user role
        $stmt = $pdo->prepare("SELECT role FROM admin_users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        $role = $user ? ucfirst(str_replace('_', ' ', $user['role'])) : 'Unknown';
        
        echo "<tr>";
        echo "<td style='padding: 10px;'><code>{$username}</code></td>";
        echo "<td style='padding: 10px;'><code>{$password}</code></td>";
        echo "<td style='padding: 10px;'>{$role}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    echo "</div>\n";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h4>⚠️ Important Security Notes</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Save these passwords securely</strong> - Store them in a password manager</li>\n";
    echo "<li><strong>Change passwords again</strong> - These are temporary secure passwords</li>\n";
    echo "<li><strong>Enable 2FA</strong> - Consider implementing two-factor authentication</li>\n";
    echo "<li><strong>Regular updates</strong> - Change passwords every 90 days</li>\n";
    echo "<li><strong>Strong passwords</strong> - New passwords must meet complexity requirements</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<h3>Password Requirements</h3>\n";
    echo "<ul>\n";
    echo "<li>✅ Minimum 8 characters</li>\n";
    echo "<li>✅ At least one uppercase letter (A-Z)</li>\n";
    echo "<li>✅ At least one lowercase letter (a-z)</li>\n";
    echo "<li>✅ At least one number (0-9)</li>\n";
    echo "<li>✅ At least one special character (@$!%*?&)</li>\n";
    echo "</ul>\n";
    
    echo "<h3>Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>✅ Test login with new credentials</li>\n";
    echo "<li>✅ Update any saved passwords</li>\n";
    echo "<li>✅ Inform other administrators</li>\n";
    echo "<li>✅ Consider implementing 2FA</li>\n";
    echo "<li>✅ Regular security audits</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='../admin/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>→ Test Admin Login</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Error details: " . htmlspecialchars($e->getTraceAsString()) . "</p>\n";
}
?>
