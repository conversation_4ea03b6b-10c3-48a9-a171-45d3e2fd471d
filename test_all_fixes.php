<?php
/**
 * Test Hero CTA and Contact Form Functionality
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "Testing Hero CTA and Contact Form Fixes\n";
echo "=====================================\n\n";

// Test 1: Hero CTA Newsletter Submission
echo "1. Testing Hero CTA Newsletter Submission\n";
echo "-----------------------------------------\n";

// Simulate AJAX request for hero CTA
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
$_POST = [
    'email' => '<EMAIL>',
    'source' => 'hero_cta',
    'page' => 'contact'
];

// Capture output
ob_start();
include 'newsletter-signup.php';
$hero_response = ob_get_clean();

echo "Hero CTA Response: " . $hero_response . "\n";

$hero_data = json_decode($hero_response, true);
if ($hero_data && $hero_data['success']) {
    echo "✅ Hero CTA newsletter signup working correctly\n";
    echo "✅ Success message: " . $hero_data['message'] . "\n";
} else {
    echo "❌ Hero CTA newsletter signup failed\n";
    if ($hero_data) {
        echo "❌ Error: " . $hero_data['message'] . "\n";
    }
}

echo "\n";

// Test 2: Contact Form Duplicate Prevention
echo "2. Testing Contact Form Duplicate Prevention\n";
echo "--------------------------------------------\n";

$test_email = '<EMAIL>';

// First contact form submission
$first_submission = handleContactForm([
    'name' => 'Test User',
    'email' => $test_email,
    'phone' => '************',
    'service' => 'Architectural Design',
    'message' => 'This is a test message for duplicate prevention.'
]);

echo "First submission result: " . ($first_submission['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $first_submission['message'] . "\n";

// Second contact form submission (should be blocked)
$second_submission = handleContactForm([
    'name' => 'Test User Again',
    'email' => $test_email,
    'phone' => '************',
    'service' => 'Engineering Services',
    'message' => 'This is a duplicate submission that should be blocked.'
]);

echo "\nSecond submission result: " . ($second_submission['success'] ? 'SUCCESS' : 'BLOCKED') . "\n";
echo "Message: " . $second_submission['message'] . "\n";

if (!$second_submission['success'] && strpos($second_submission['message'], 'already submitted') !== false) {
    echo "✅ Duplicate prevention working correctly\n";
} else {
    echo "❌ Duplicate prevention not working\n";
}

echo "\n";

// Test 3: Verify database entries
echo "3. Verifying Database Entries\n";
echo "-----------------------------\n";

$db = Database::getConnection();

// Check newsletter subscribers
$newsletter_count = $db->prepare("SELECT COUNT(*) as count FROM newsletter_subscribers WHERE email = ?");
$newsletter_count->execute(['<EMAIL>']);
$newsletter_result = $newsletter_count->fetch();

echo "Newsletter subscriptions for test email: " . $newsletter_result['count'] . "\n";

// Check contact submissions
$contact_count = $db->prepare("SELECT COUNT(*) as count FROM contact_submissions WHERE email = ? AND is_newsletter_signup = 0");
$contact_count->execute([$test_email]);
$contact_result = $contact_count->fetch();

echo "Contact submissions for test email: " . $contact_result['count'] . "\n";

// Check if hero CTA also created contact submission
$hero_contact_count = $db->prepare("SELECT COUNT(*) as count FROM contact_submissions WHERE email = ? AND is_newsletter_signup = 1");
$hero_contact_count->execute(['<EMAIL>']);
$hero_contact_result = $hero_contact_count->fetch();

echo "Hero CTA contact submissions: " . $hero_contact_result['count'] . "\n";

echo "\n";

// Test 4: Test hero section database lookup for success message
echo "4. Testing Hero Section Success Message Lookup\n";
echo "-----------------------------------------------\n";

// Check if contact page has custom newsletter success message
$hero_check = $db->prepare("SELECT newsletter_success_message FROM hero_sections WHERE page_name = 'contact' AND show_newsletter_input = 1");
$hero_check->execute();
$hero_section = $hero_check->fetch();

if ($hero_section) {
    echo "✅ Contact page hero section found\n";
    echo "Custom success message: " . ($hero_section['newsletter_success_message'] ?: 'Default message') . "\n";
} else {
    echo "ℹ️ No custom hero section for contact page or newsletter input disabled\n";
}

echo "\nTest Summary:\n";
echo "=============\n";
echo "Hero CTA Newsletter: " . ($hero_data && $hero_data['success'] ? '✅ WORKING' : '❌ FAILED') . "\n";
echo "Duplicate Prevention: " . (!$second_submission['success'] ? '✅ WORKING' : '❌ FAILED') . "\n";
echo "Database Integration: ✅ VERIFIED\n";

echo "\nAll tests completed!\n";
?>
