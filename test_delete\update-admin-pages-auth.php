<?php
/**
 * Update Admin Pages with New Authentication System
 * Adds role-based access control to all admin pages
 */

$adminPages = [
    'blog.php',
    'email-settings.php', 
    'footer.php',
    'hero-headers.php',
    'hero-sections.php',
    'newsletter-signup.php',
    'projects.php',
    'services.php',
    'sliders.php',
    'team.php',
    'testimonials.php',
    'theme-demo.php'
];

$oldAuthPattern = '/\/\/ Simple authentication\s*\nif \(!isset\(\$_SESSION\[\'admin_logged_in\'\]\)\) \{\s*\n\s*header\(\'Location: login\.php\'\);\s*\n\s*exit;\s*\n\}/';

$newAuthCode = '// Require Super Admin access
requireSuperAdmin();';

$includePattern = '/require_once \'\.\.\/includes\/functions\.php\';/';
$newIncludeCode = 'require_once \'../includes/functions.php\';
require_once \'../includes/admin-auth.php\';';

echo "<h2>Updating Admin Pages with New Authentication</h2>\n";

foreach ($adminPages as $page) {
    $filePath = __DIR__ . '/../admin/' . $page;
    
    if (!file_exists($filePath)) {
        echo "<p style='color: orange;'>⚠ Skipping {$page} - file not found</p>\n";
        continue;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Add admin-auth.php include
    if (strpos($content, 'admin-auth.php') === false) {
        $content = preg_replace($includePattern, $newIncludeCode, $content);
    }
    
    // Replace old authentication with new
    if (preg_match($oldAuthPattern, $content)) {
        $content = preg_replace($oldAuthPattern, $newAuthCode, $content);
        echo "<p style='color: green;'>✓ Updated {$page} with new authentication</p>\n";
    } else {
        // Try alternative pattern
        $altPattern = '/if \(!isset\(\$_SESSION\[\'admin_logged_in\'\]\)\) \{\s*\n\s*header\(\'Location: login\.php\'\);\s*\n\s*exit;\s*\n\}/';
        if (preg_match($altPattern, $content)) {
            $content = preg_replace($altPattern, $newAuthCode, $content);
            echo "<p style='color: green;'>✓ Updated {$page} with new authentication (alt pattern)</p>\n";
        } else {
            echo "<p style='color: blue;'>ℹ {$page} - no standard auth pattern found, may need manual update</p>\n";
        }
    }
    
    // Write back if changed
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
    }
}

echo "<h3>Manual Updates Needed</h3>\n";
echo "<p>The following files may need manual authentication updates:</p>\n";
echo "<ul>\n";
echo "<li><code>admin/login.php</code> - Already updated</li>\n";
echo "<li><code>admin/logout.php</code> - Already updated</li>\n";
echo "<li><code>admin/contacts.php</code> - Already updated</li>\n";
echo "<li><code>admin/index.php</code> - Already updated</li>\n";
echo "</ul>\n";

echo "<h3>Next Steps</h3>\n";
echo "<ol>\n";
echo "<li>Update admin navigation for role-based display</li>\n";
echo "<li>Update admin header with user role indicator</li>\n";
echo "<li>Create user management interface</li>\n";
echo "<li>Test all pages with different user roles</li>\n";
echo "</ol>\n";

echo "<p><a href='../admin/login.php'>← Go to Admin Login</a></p>\n";
?>
