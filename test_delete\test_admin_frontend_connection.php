<?php
/**
 * Test Admin/Frontend Connection for Hero Headers
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h1>Hero Headers Admin/Frontend Connection Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 2rem; }
    .success { color: #27ae60; }
    .error { color: #e74c3c; }
    .info { color: #3498db; }
    .warning { color: #f39c12; }
    .test-section { margin: 2rem 0; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; }
    table { border-collapse: collapse; width: 100%; margin: 1rem 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .test-link { display: inline-block; margin: 0.5rem; padding: 0.5rem 1rem; background: #3498db; color: white; text-decoration: none; border-radius: 4px; }
</style>";

echo "<div class='test-section'>";
echo "<h2>🔍 System Status Check</h2>";

$test_pages = ['team', 'service-details'];

echo "<table>";
echo "<tr><th>Page</th><th>Template System</th><th>Database Record</th><th>Admin Link</th><th>Frontend Link</th></tr>";

foreach ($test_pages as $page) {
    $hero = getHeroHeader($page);
    
    echo "<tr>";
    echo "<td><strong>$page</strong></td>";
    
    // Check template system
    $file_path = $page === 'service-details' ? 'service-details.php' : "$page.php";
    if (file_exists($file_path)) {
        $content = file_get_contents($file_path);
        if (strpos($content, "loadTemplate('hero-header')") !== false) {
            echo "<td class='success'>✅ Hero Headers</td>";
        } elseif (strpos($content, 'templates/hero-header.php') !== false) {
            echo "<td class='success'>✅ Hero Headers</td>";
        } elseif (strpos($content, 'templates/page-hero.php') !== false) {
            echo "<td class='error'>❌ Old Page Hero</td>";
        } else {
            echo "<td class='warning'>⚠️ Unknown</td>";
        }
    } else {
        echo "<td class='error'>❌ File Missing</td>";
    }
    
    // Check database record
    if ($hero && $hero['active']) {
        echo "<td class='success'>✅ Active Record</td>";
    } elseif ($hero) {
        echo "<td class='warning'>⚠️ Inactive Record</td>";
    } else {
        echo "<td class='error'>❌ No Record</td>";
    }
    
    // Admin and frontend links
    echo "<td><a href='admin/hero-headers.php' class='test-link' target='_blank'>Admin Panel</a></td>";
    
    $frontend_url = $page === 'service-details' ? 'service-details.php?slug=structural-engineering' : "$page.php";
    echo "<td><a href='$frontend_url' class='test-link' target='_blank'>View Page</a></td>";
    
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🔧 Current Hero Header Data</h2>";

foreach ($test_pages as $page) {
    echo "<h3>$page</h3>";
    $hero = getHeroHeader($page);
    
    if ($hero) {
        echo "<ul>";
        echo "<li><strong>Page Title:</strong> " . htmlspecialchars($hero['page_title']) . "</li>";
        echo "<li><strong>Subtitle:</strong> " . htmlspecialchars($hero['subtitle'] ?? 'N/A') . "</li>";
        echo "<li><strong>Background Type:</strong> " . ($hero['background_type'] ?? 'N/A') . "</li>";
        echo "<li><strong>Background Image:</strong> " . ($hero['background_image'] ? 'Yes' : 'No') . "</li>";
        echo "<li><strong>Show Breadcrumbs:</strong> " . ($hero['show_breadcrumbs'] ? 'Yes' : 'No') . "</li>";
        echo "<li><strong>Show CTA Button:</strong> " . ($hero['show_cta_button'] ? 'Yes' : 'No') . "</li>";
        echo "<li><strong>Active:</strong> " . ($hero['active'] ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
    } else {
        echo "<p class='error'>❌ No hero header data found</p>";
    }
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>📋 Test Instructions</h2>";
echo "<ol>";
echo "<li>Click 'Admin Panel' above to access hero headers management</li>";
echo "<li>Edit the hero header for 'team' or 'service-details'</li>";
echo "<li>Change the title, subtitle, or background</li>";
echo "<li>Save the changes</li>";
echo "<li>Click 'View Page' to see if changes appear on frontend</li>";
echo "<li>If changes appear immediately, the connection is working!</li>";
echo "</ol>";
echo "</div>";

?>
