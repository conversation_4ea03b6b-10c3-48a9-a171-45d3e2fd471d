<?php
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

try {
    $db = Database::getConnection();
    $stmt = $db->prepare('SELECT page_name, page_title, active FROM hero_headers WHERE page_name LIKE "%service%" ORDER BY page_name');
    $stmt->execute();
    $headers = $stmt->fetchAll();
    
    echo "Service-related hero headers:\n";
    foreach ($headers as $header) {
        echo "- {$header['page_name']}: '{$header['page_title']}' (active: {$header['active']})\n";
    }
    
    // Also check for any 'service-details' page
    $stmt = $db->prepare('SELECT page_name, page_title, active FROM hero_headers WHERE page_name = "service-details"');
    $stmt->execute();
    $static_header = $stmt->fetch();
    
    if ($static_header) {
        echo "\nFound static service-details header:\n";
        echo "- {$static_header['page_name']}: '{$static_header['page_title']}' (active: {$static_header['active']})\n";
    } else {
        echo "\nNo static 'service-details' header found.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
