<?php
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

$db = Database::getConnection();
$stmt = $db->prepare('SELECT page_name, page_title, active FROM hero_headers WHERE page_name IN (?, ?)');
$stmt->execute(['team', 'service-details']);
$records = $stmt->fetchAll();

echo "Hero Headers Records:\n";
foreach ($records as $record) {
    $status = $record['active'] ? 'ACTIVE' : 'INACTIVE';
    echo "- {$record['page_name']}: {$record['page_title']} ({$status})\n";
}

if (empty($records)) {
    echo "No records found for team or service-details pages.\n";
}
?>
