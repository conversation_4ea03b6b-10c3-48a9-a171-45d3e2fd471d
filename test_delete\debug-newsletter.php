<?php
/**
 * Debug Newsletter Signup
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>NEWSLETTER SIGNUP DEBUG</h1><pre>";

// Test 1: Check if newsletter-signup.php exists
echo "📋 TASK 1: Check Newsletter Signup File\n";
echo "----------------------------------------\n";

$newsletter_file = __DIR__ . '/../newsletter-signup.php';
if (file_exists($newsletter_file)) {
    echo "✅ newsletter-signup.php exists\n";
    echo "📁 File path: $newsletter_file\n";
    echo "📏 File size: " . filesize($newsletter_file) . " bytes\n";
} else {
    echo "❌ newsletter-signup.php NOT found\n";
}

echo "\n";

// Test 2: Simulate POST request directly
echo "📋 TASK 2: Simulate POST Request\n";
echo "---------------------------------\n";

// Save original values
$original_method = $_SERVER['REQUEST_METHOD'] ?? '';
$original_post = $_POST;

// Set up POST simulation
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST = [
    'email' => '<EMAIL>',
    'source' => 'debug',
    'page' => 'debug_page'
];

echo "🔧 Simulating POST request:\n";
echo "   Method: " . $_SERVER['REQUEST_METHOD'] . "\n";
echo "   Email: " . $_POST['email'] . "\n";
echo "   Source: " . $_POST['source'] . "\n";
echo "   Page: " . $_POST['page'] . "\n";

try {
    // Capture output from newsletter-signup.php
    ob_start();
    
    // Include the newsletter signup handler
    include $newsletter_file;
    
    $output = ob_get_clean();
    
    echo "\n📤 Output from newsletter-signup.php:\n";
    echo "Raw output: " . htmlspecialchars($output) . "\n";
    
    // Try to parse as JSON
    $json_data = json_decode($output, true);
    if ($json_data) {
        echo "✅ Valid JSON response\n";
        echo "Success: " . ($json_data['success'] ? 'true' : 'false') . "\n";
        echo "Message: " . $json_data['message'] . "\n";
    } else {
        echo "❌ Invalid JSON response\n";
        echo "JSON Error: " . json_last_error_msg() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

// Restore original values
$_SERVER['REQUEST_METHOD'] = $original_method;
$_POST = $original_post;

echo "\n";

// Test 3: Check database connection and tables
echo "📋 TASK 3: Check Database\n";
echo "--------------------------\n";

try {
    $db = Database::getConnection();
    echo "✅ Database connection successful\n";
    
    // Check newsletter_subscribers table
    $stmt = $db->prepare("SHOW TABLES LIKE 'newsletter_subscribers'");
    $stmt->execute();
    if ($stmt->fetch()) {
        echo "✅ newsletter_subscribers table exists\n";
        
        // Check table structure
        $stmt = $db->prepare("DESCRIBE newsletter_subscribers");
        $stmt->execute();
        $columns = $stmt->fetchAll();
        echo "📊 Table columns:\n";
        foreach ($columns as $col) {
            echo "   - {$col['Field']}: {$col['Type']}\n";
        }
        
        // Check recent entries
        $stmt = $db->prepare("SELECT * FROM newsletter_subscribers ORDER BY subscribed_at DESC LIMIT 3");
        $stmt->execute();
        $recent = $stmt->fetchAll();
        echo "\n📈 Recent subscribers:\n";
        if ($recent) {
            foreach ($recent as $sub) {
                echo "   📧 {$sub['email']} ({$sub['source']}) - {$sub['subscribed_at']}\n";
            }
        } else {
            echo "   ℹ️ No subscribers found\n";
        }
    } else {
        echo "❌ newsletter_subscribers table does not exist\n";
    }
    
    // Check contact_submissions table
    $stmt = $db->prepare("SHOW TABLES LIKE 'contact_submissions'");
    $stmt->execute();
    if ($stmt->fetch()) {
        echo "\n✅ contact_submissions table exists\n";
        
        // Check for newsletter submissions
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM contact_submissions WHERE is_newsletter_signup = 1");
        $stmt->execute();
        $count = $stmt->fetch()['count'];
        echo "📊 Newsletter contact submissions: $count\n";
    } else {
        echo "\n❌ contact_submissions table does not exist\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Check functions
echo "📋 TASK 4: Check Required Functions\n";
echo "------------------------------------\n";

$required_functions = ['sanitizeInput', 'sendEmail'];
foreach ($required_functions as $func) {
    if (function_exists($func)) {
        echo "✅ Function $func exists\n";
    } else {
        echo "❌ Function $func missing\n";
    }
}

// Test email validation
$test_email = '<EMAIL>';
$validated = filter_var($test_email, FILTER_VALIDATE_EMAIL);
echo "\n📧 Email validation test:\n";
echo "   Input: $test_email\n";
echo "   Result: " . ($validated ? $validated : 'INVALID') . "\n";

echo "\n";

// Test 5: Manual newsletter signup test
echo "📋 TASK 5: Manual Newsletter Signup Test\n";
echo "-----------------------------------------\n";

try {
    $test_email = 'manual-test-' . time() . '@example.com';
    $db = Database::getConnection();
    
    // Insert directly into newsletter_subscribers
    $stmt = $db->prepare("
        INSERT INTO newsletter_subscribers (email, source, page, subscribed_at, status) 
        VALUES (?, ?, ?, NOW(), 'active')
    ");
    
    if ($stmt->execute([$test_email, 'manual_test', 'debug'])) {
        echo "✅ Manual newsletter signup successful\n";
        echo "📧 Email: $test_email\n";
        
        // Verify insertion
        $stmt = $db->prepare("SELECT * FROM newsletter_subscribers WHERE email = ?");
        $stmt->execute([$test_email]);
        $result = $stmt->fetch();
        
        if ($result) {
            echo "✅ Verification successful - record found in database\n";
            echo "📊 ID: {$result['id']}, Status: {$result['status']}\n";
        } else {
            echo "❌ Verification failed - record not found\n";
        }
    } else {
        echo "❌ Manual newsletter signup failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Manual test error: " . $e->getMessage() . "\n";
}

echo "\n🎯 DEBUG SUMMARY\n";
echo "================\n";
echo "Check the results above to identify the issue.\n";
echo "Common issues:\n";
echo "1. File permissions\n";
echo "2. Database connection\n";
echo "3. Missing functions\n";
echo "4. Table structure\n";
echo "5. POST data handling\n";

echo "</pre>";
?>
