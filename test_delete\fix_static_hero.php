<?php
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

try {
    $db = Database::getConnection();
    
    // Disable the static 'service-details' hero header since it conflicts with dynamic ones
    $stmt = $db->prepare('UPDATE hero_headers SET active = 0 WHERE page_name = "service-details"');
    $result = $stmt->execute();
    
    if ($result) {
        echo "Successfully disabled static 'service-details' hero header.\n";
        
        // Verify it's disabled
        $stmt = $db->prepare('SELECT page_name, page_title, active FROM hero_headers WHERE page_name = "service-details"');
        $stmt->execute();
        $header = $stmt->fetch();
        
        if ($header) {
            echo "Verification: service-details header is now active = {$header['active']}\n";
        }
    } else {
        echo "Failed to update the hero header.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
