<?php
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "Testing getHeroHeader function:\n\n";

$test_pages = ['team', 'service-details'];

foreach ($test_pages as $page) {
    echo "Testing page: $page\n";
    $hero = getHeroHeader($page);
    
    if ($hero) {
        echo "✅ Found hero header\n";
        echo "  - Title: {$hero['page_title']}\n";
        echo "  - Subtitle: " . ($hero['subtitle'] ?? 'N/A') . "\n";
        echo "  - Active: " . ($hero['active'] ? 'Yes' : 'No') . "\n";
        echo "  - Background Type: " . ($hero['background_type'] ?? 'N/A') . "\n";
    } else {
        echo "❌ No hero header found\n";
    }
    echo "\n";
}
?>
