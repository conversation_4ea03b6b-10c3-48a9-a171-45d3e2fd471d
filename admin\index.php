<?php
/**
 * Monolith Design Co. - Admin Dashboard
 * Now using the new Admin Theme System
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/admin-auth.php';

// Require Super Admin access for theme settings
requireSuperAdmin();

// Handle form submissions
$message = '';
$error = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'update_general':
                    updateThemeOption('site_name', sanitizeInput($_POST['site_name']));
                    updateThemeOption('site_tagline', sanitizeInput($_POST['site_tagline']));
                    updateThemeOption('phone_number', sanitizeInput($_POST['phone_number']));
                    updateThemeOption('email', sanitizeInput($_POST['email']));
                    updateThemeOption('address', sanitizeInput($_POST['address']));
                    updateThemeOption('admin_header_display', sanitizeInput($_POST['admin_header_display']));
                    $message = 'General settings updated successfully!';
                    break;

                case 'update_colors':
                    updateThemeOption('accent_color', sanitizeInput($_POST['accent_color']));
                    updateThemeOption('primary_color', sanitizeInput($_POST['primary_color']));
                    updateThemeOption('secondary_color', sanitizeInput($_POST['secondary_color']));
                    updateThemeOption('table_header_bg', sanitizeInput($_POST['table_header_bg']));
                    updateThemeOption('table_header_text', sanitizeInput($_POST['table_header_text']));
                    // Save logo size percentage if provided
                    if (isset($_POST['logo_size_percent'])) {
                        $logoSizePercent = intval($_POST['logo_size_percent']);
                        // Validate range (50-200%)
                        if ($logoSizePercent >= 50 && $logoSizePercent <= 200) {
                            updateThemeOption('logo_size_percent', $logoSizePercent);
                        }
                    }
                    // Legacy support: convert old logo_size to percentage
                    if (isset($_POST['logo_size'])) {
                        $legacySize = sanitizeInput($_POST['logo_size']);
                        $percentageMap = ['small' => 75, 'medium' => 100, 'large' => 150];
                        if (isset($percentageMap[$legacySize])) {
                            updateThemeOption('logo_size_percent', $percentageMap[$legacySize]);
                        }
                    }
                    $message = 'Color settings updated successfully!';
                    break;

                case 'update_social':
                    updateThemeOption('facebook_url', sanitizeInput($_POST['facebook_url']));
                    updateThemeOption('twitter_url', sanitizeInput($_POST['twitter_url']));
                    updateThemeOption('linkedin_url', sanitizeInput($_POST['linkedin_url']));
                    updateThemeOption('instagram_url', sanitizeInput($_POST['instagram_url']));
                    $message = 'Social media links updated successfully!';
                    break;

                case 'update_footer':
                    updateThemeOption('footer_about_text', sanitizeInput($_POST['footer_about_text']));
                    updateThemeOption('footer_copyright', sanitizeInput($_POST['footer_copyright']));
                    $message = 'Footer content updated successfully!';
                    break;

                case 'upload_logo':
                    if (isset($_FILES['logo_file']) && $_FILES['logo_file']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['logo_file'], ['jpg', 'jpeg', 'png', 'svg']);
                        if ($upload['success']) {
                            updateThemeOption('site_logo', $upload['url']);
                            $message = 'Logo uploaded successfully!';
                        } else {
                            $error = 'Error uploading logo: ' . $upload['message'];
                        }
                    }
                    break;

                case 'upload_white_logo':
                    if (isset($_FILES['white_logo_file']) && $_FILES['white_logo_file']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['white_logo_file'], ['jpg', 'jpeg', 'png', 'svg']);
                        if ($upload['success']) {
                            updateThemeOption('site_logo_white', $upload['url']);
                            $message = 'White logo uploaded successfully!';
                        } else {
                            $error = 'Error uploading white logo: ' . $upload['message'];
                        }
                    }
                    break;

                case 'upload_favicon':
                    if (isset($_FILES['favicon_file']) && $_FILES['favicon_file']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['favicon_file'], ['ico', 'png', 'svg']);
                        if ($upload['success']) {
                            updateThemeOption('favicon', $upload['url']);
                            $message = 'Favicon uploaded successfully!';
                        } else {
                            $error = 'Error uploading favicon: ' . $upload['message'];
                        }
                    }
                    break;

                case 'upload_white_logo':
                    if (isset($_FILES['white_logo_file']) && $_FILES['white_logo_file']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['white_logo_file'], ['jpg', 'jpeg', 'png', 'svg']);
                        if ($upload['success']) {
                            updateThemeOption('site_logo_white', $upload['url']);
                            $message = 'White logo uploaded successfully!';
                        } else {
                            $error = 'Error uploading white logo: ' . $upload['message'];
                        }
                    }
                    break;



                case 'update_contact':
                    updateThemeOption('contact_hero_title', sanitizeInput($_POST['contact_hero_title']));
                    updateThemeOption('contact_hero_description', sanitizeInput($_POST['contact_hero_description']));
                    updateThemeOption('contact_info_title', sanitizeInput($_POST['contact_info_title']));
                    updateThemeOption('contact_info_description', sanitizeInput($_POST['contact_info_description']));
                    updateThemeOption('contact_form_title', sanitizeInput($_POST['contact_form_title']));
                    updateThemeOption('contact_form_description', sanitizeInput($_POST['contact_form_description']));
                    updateThemeOption('contact_map_title', sanitizeInput($_POST['contact_map_title']));
                    updateThemeOption('contact_map_embed', $_POST['contact_map_embed']); // Don't sanitize HTML
                    updateThemeOption('contact_show_map', sanitizeInput($_POST['contact_show_map']));
                    updateThemeOption('business_hours', sanitizeInput($_POST['business_hours']));

                    // Handle background image upload
                    if (isset($_FILES['contact_hero_background']) && $_FILES['contact_hero_background']['error'] === UPLOAD_ERR_OK) {
                        $uploadDir = '../assets/images/uploads/';
                        if (!file_exists($uploadDir)) {
                            mkdir($uploadDir, 0755, true);
                        }

                        $fileName = 'contact-hero-bg-' . time() . '.' . pathinfo($_FILES['contact_hero_background']['name'], PATHINFO_EXTENSION);
                        $uploadPath = $uploadDir . $fileName;

                        if (move_uploaded_file($_FILES['contact_hero_background']['tmp_name'], $uploadPath)) {
                            updateThemeOption('contact_hero_background', 'assets/images/uploads/' . $fileName);
                        }
                    }

                    $message = 'Contact page settings updated successfully!';
                    break;

                case 'update_login_page':
                    updateThemeOption('login_background_type', sanitizeInput($_POST['login_background_type']));
                    updateThemeOption('login_background_color', sanitizeInput($_POST['login_background_color']));
                    updateThemeOption('login_background_gradient_start', sanitizeInput($_POST['login_background_gradient_start']));
                    updateThemeOption('login_background_gradient_end', sanitizeInput($_POST['login_background_gradient_end']));
                    updateThemeOption('login_background_gradient_direction', sanitizeInput($_POST['login_background_gradient_direction']));
                    updateThemeOption('login_overlay_opacity', sanitizeInput($_POST['login_overlay_opacity']));
                    updateThemeOption('login_overlay_color', sanitizeInput($_POST['login_overlay_color']));
                    updateThemeOption('login_particles_enabled', isset($_POST['login_particles_enabled']) ? '1' : '0');
                    updateThemeOption('login_particles_url', sanitizeInput($_POST['login_particles_url']));
                    updateThemeOption('login_animation_enabled', isset($_POST['login_animation_enabled']) ? '1' : '0');

                    // Handle background image upload
                    if (isset($_FILES['login_background_image']) && $_FILES['login_background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['login_background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload['success']) {
                            updateThemeOption('login_background_image', $upload['url']);
                        } else {
                            $error = 'Error uploading login background image: ' . $upload['message'];
                            break;
                        }
                    }

                    // Handle background video upload
                    if (isset($_FILES['login_background_video']) && $_FILES['login_background_video']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['login_background_video'], ['mp4', 'webm', 'ogg']);
                        if ($upload['success']) {
                            updateThemeOption('login_background_video', $upload['url']);
                        } else {
                            $error = 'Error uploading login background video: ' . $upload['message'];
                            break;
                        }
                    }

                    $message = 'Login page settings updated successfully!';
                    break;
            }
        } catch (Exception $e) {
            $error = 'Error processing request: ' . $e->getMessage();
        }
    }
}

// Get current settings
$settings = [
    'site_name' => getThemeOption('site_name', SITE_NAME),
    'site_tagline' => getThemeOption('site_tagline', SITE_TAGLINE),
    'phone_number' => getThemeOption('phone_number', '+****************'),
    'email' => getThemeOption('email', '<EMAIL>'),
    'address' => getThemeOption('address', '123 Design Street\nArchitecture City, AC 12345'),
    'admin_header_display' => getThemeOption('admin_header_display', 'both'),
    'accent_color' => getThemeOption('accent_color', '#E67E22'),
    'primary_color' => getThemeOption('primary_color', '#1A1A1A'),
    'secondary_color' => getThemeOption('secondary_color', '#F5F5F5'),
    'logo_size' => getThemeOption('logo_size', 'medium'),
    'facebook_url' => getThemeOption('facebook_url', ''),
    'twitter_url' => getThemeOption('twitter_url', ''),
    'linkedin_url' => getThemeOption('linkedin_url', ''),
    'instagram_url' => getThemeOption('instagram_url', ''),
    'footer_about_text' => getThemeOption('footer_about_text', 'We are a leading engineering and architectural firm dedicated to creating innovative structures with precision and excellence.'),
    'footer_copyright' => getThemeOption('footer_copyright', 'All rights reserved.'),
    'site_logo' => getThemeOption('site_logo', themeUrl('images/logo.svg')),
    'favicon' => getThemeOption('favicon', themeUrl('images/favicon.ico')),
    'contact_hero_title' => getThemeOption('contact_hero_title', 'Get In Touch'),
    'contact_hero_description' => getThemeOption('contact_hero_description', 'Ready to start your project? We\'re here to help bring your vision to life with our expertise in engineering and architectural design.'),
    'contact_hero_background' => getThemeOption('contact_hero_background', ''),
    'contact_info_title' => getThemeOption('contact_info_title', 'Contact Information'),
    'contact_info_description' => getThemeOption('contact_info_description', 'Get in touch with us today to discuss your project requirements.'),
    'contact_form_title' => getThemeOption('contact_form_title', 'Send us a Message'),
    'contact_form_description' => getThemeOption('contact_form_description', 'Fill out the form below and we\'ll get back to you as soon as possible.'),
    'contact_map_title' => getThemeOption('contact_map_title', 'Find Us'),
    'contact_map_embed' => getThemeOption('contact_map_embed', ''),
    'contact_show_map' => getThemeOption('contact_show_map', '1'),
    'business_hours' => getThemeOption('business_hours', 'Monday - Friday: 8:00 AM - 6:00 PM\nSaturday: 9:00 AM - 4:00 PM\nSunday: Closed'),
    'table_header_bg' => getThemeOption('table_header_bg', '#f8f9fa'),
    'table_header_text' => getThemeOption('table_header_text', '#495057'),

    // Login Page Settings
    'login_background_type' => getThemeOption('login_background_type', 'gradient'),
    'login_background_color' => getThemeOption('login_background_color', '#1A1A1A'),
    'login_background_gradient_start' => getThemeOption('login_background_gradient_start', '#1A1A1A'),
    'login_background_gradient_end' => getThemeOption('login_background_gradient_end', '#E67E22'),
    'login_background_gradient_direction' => getThemeOption('login_background_gradient_direction', '45deg'),
    'login_background_image' => getThemeOption('login_background_image', ''),
    'login_background_video' => getThemeOption('login_background_video', ''),
    'login_overlay_opacity' => getThemeOption('login_overlay_opacity', '0.7'),
    'login_overlay_color' => getThemeOption('login_overlay_color', '#000000'),
    'login_particles_enabled' => getThemeOption('login_particles_enabled', '1'),
    'login_particles_url' => getThemeOption('login_particles_url', ''),
    'login_animation_enabled' => getThemeOption('login_animation_enabled', '1')
];

// Render the page using the new theme system
renderAdminPage('settings', [
    'page_title' => 'Theme Options',
    'page_icon' => 'fas fa-palette',
    'page_description' => 'Configure your website settings, colors, branding, and content.',
    'show_page_header' => true,
    'content_file' => __DIR__ . '/theme/content/theme-settings.php',
    'message' => $message,
    'error' => $error,
    'settings' => $settings
]);
?>