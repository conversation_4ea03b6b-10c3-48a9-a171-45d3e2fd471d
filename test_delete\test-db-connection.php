<?php
/**
 * Test Database Connection
 */

// Try different database configurations
$configs = [
    ['host' => 'localhost', 'user' => 'root', 'pass' => ''],
    ['host' => 'localhost', 'user' => 'root', 'pass' => 'root'],
    ['host' => 'localhost', 'user' => 'root', 'pass' => 'password'],
    ['host' => '127.0.0.1', 'user' => 'root', 'pass' => ''],
    ['host' => '127.0.0.1', 'user' => 'root', 'pass' => 'root'],
];

$dbname = 'monolith_design';

echo "<h2>Database Connection Test</h2>\n";

foreach ($configs as $i => $config) {
    echo "<h3>Test " . ($i + 1) . ": {$config['user']}@{$config['host']} with password: " . ($config['pass'] ? 'YES' : 'NO') . "</h3>\n";
    
    try {
        $pdo = new PDO("mysql:host={$config['host']};charset=utf8", $config['user'], $config['pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p style='color: green;'>✓ Connection successful!</p>\n";
        
        // Try to select the database
        try {
            $pdo->exec("USE $dbname");
            echo "<p style='color: green;'>✓ Database '$dbname' exists and accessible</p>\n";
            
            // List existing tables
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($tables)) {
                echo "<p style='color: orange;'>⚠ Database is empty (no tables)</p>\n";
            } else {
                echo "<p style='color: blue;'>ℹ Existing tables:</p>\n";
                echo "<ul>\n";
                foreach ($tables as $table) {
                    echo "<li>$table</li>\n";
                }
                echo "</ul>\n";
            }
            
            // Check if admin_users table exists
            $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✓ admin_users table already exists</p>\n";
                
                // Show existing users
                $stmt = $pdo->query("SELECT username, email, role, status FROM admin_users");
                $users = $stmt->fetchAll();
                
                if (!empty($users)) {
                    echo "<p><strong>Existing admin users:</strong></p>\n";
                    echo "<table border='1' style='border-collapse: collapse;'>\n";
                    echo "<tr><th>Username</th><th>Email</th><th>Role</th><th>Status</th></tr>\n";
                    foreach ($users as $user) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['role']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['status']) . "</td>";
                        echo "</tr>\n";
                    }
                    echo "</table>\n";
                }
            } else {
                echo "<p style='color: orange;'>⚠ admin_users table does not exist</p>\n";
            }
            
            echo "<p><strong>This configuration works! Use these settings:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Host: {$config['host']}</li>\n";
            echo "<li>Username: {$config['user']}</li>\n";
            echo "<li>Password: " . ($config['pass'] ? $config['pass'] : '(empty)') . "</li>\n";
            echo "<li>Database: $dbname</li>\n";
            echo "</ul>\n";
            
            break; // Stop testing once we find a working configuration
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Cannot access database '$dbname': " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Connection failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    echo "<hr>\n";
}

echo "<p><a href='../admin/login.php'>→ Go to Admin Login</a></p>\n";
?>
