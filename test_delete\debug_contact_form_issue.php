<?php
/**
 * Debug Contact Form Issue - Check POST Data and Response
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h1>🔍 CONTACT FORM DEBUG - REAL SUBMISSION TEST</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:10px;border-radius:4px;margin:5px 0;} 
.error{color:red;background:#f8d7da;padding:10px;border-radius:4px;margin:5px 0;} 
.info{color:blue;background:#d1ecf1;padding:10px;border-radius:4px;margin:5px 0;} 
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:5px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow-x:auto;}
.form-message{padding:1rem 1.5rem;border-radius:8px;margin:1rem 0;font-weight:500;}
.form-message.success{background-color:#d4edda;color:#155724;border:1px solid #c3e6cb;}
.form-message.error{background-color:#f8d7da;color:#721c24;border:1px solid #f5c6cb;}
</style>";

// Check POST data
echo "<h2>📋 Step 1: POST Data Analysis</h2>";
if ($_POST) {
    echo "<div class='info'>📝 POST data received:</div>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    if (isset($_POST['submit_contact'])) {
        echo "<div class='success'>✅ submit_contact button was clicked</div>";
    } else {
        echo "<div class='error'>❌ submit_contact button was NOT detected</div>";
    }
} else {
    echo "<div class='warning'>⚠️ No POST data received. This is a GET request.</div>";
}

// Simulate contact form processing
$form_message = '';
$form_status = '';
if ($_POST && isset($_POST['submit_contact'])) {
    echo "<h2>🧪 Step 2: Processing Form Submission</h2>";
    
    $result = handleContactForm($_POST);
    $form_message = $result['message'];
    $form_status = $result['success'] ? 'success' : 'error';
    
    echo "<div class='info'>📊 handleContactForm Result:</div>";
    echo "<pre>" . print_r($result, true) . "</pre>";
    echo "<div class='info'>📝 form_message = '{$form_message}'</div>";
    echo "<div class='info'>📝 form_status = '{$form_status}'</div>";
    
    if ($form_message) {
        echo "<div class='warning'>🎨 Success Message Preview:</div>";
        echo "<div class='form-message {$form_status}'>";
        echo htmlspecialchars($form_message);
        echo "</div>";
    }
}

echo "<h2>🎯 Step 3: Contact Form Test</h2>";
echo "<div class='info'>Use this form to test the contact functionality:</div>";

?>

<form method="POST" action="" style="background:white;padding:20px;border-radius:8px;margin:20px 0;">
    <div style="margin-bottom:15px;">
        <label for="name" style="display:block;margin-bottom:5px;font-weight:bold;">Name:</label>
        <input type="text" id="name" name="name" required style="width:300px;padding:8px;border:1px solid #ccc;border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label for="email" style="display:block;margin-bottom:5px;font-weight:bold;">Email:</label>
        <input type="email" id="email" name="email" required style="width:300px;padding:8px;border:1px solid #ccc;border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label for="phone" style="display:block;margin-bottom:5px;font-weight:bold;">Phone:</label>
        <input type="tel" id="phone" name="phone" style="width:300px;padding:8px;border:1px solid #ccc;border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label for="service" style="display:block;margin-bottom:5px;font-weight:bold;">Service:</label>
        <select id="service" name="service" style="width:316px;padding:8px;border:1px solid #ccc;border-radius:4px;">
            <option value="">Select a service...</option>
            <option value="Test Service">Test Service</option>
        </select>
    </div>
    
    <div style="margin-bottom:15px;">
        <label for="message" style="display:block;margin-bottom:5px;font-weight:bold;">Message:</label>
        <textarea id="message" name="message" required rows="4" style="width:300px;padding:8px;border:1px solid #ccc;border-radius:4px;"></textarea>
    </div>
    
    <button type="submit" name="submit_contact" style="background:#2D5A27;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;font-weight:bold;">
        Submit Test Contact
    </button>
</form>

<?php
echo "<div class='info'>💡 What to look for:</div>";
echo "<div class='info'>1. Fill out the form above and submit</div>";
echo "<div class='info'>2. Check if POST data is received correctly</div>";
echo "<div class='info'>3. Verify that handleContactForm is called</div>";
echo "<div class='info'>4. See if success message is generated</div>";
?>
