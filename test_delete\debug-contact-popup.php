<?php
/**
 * Debug Contact Form Popup Issue
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>🔍 CONTACT FORM POPUP DEBUG</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:10px;border-radius:4px;margin:5px 0;} 
.error{color:red;background:#f8d7da;padding:10px;border-radius:4px;margin:5px 0;} 
.info{color:blue;background:#d1ecf1;padding:10px;border-radius:4px;margin:5px 0;} 
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:5px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow-x:auto;}
.test-section{background:white;padding:20px;margin:15px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1);}
</style>";

try {
    $db = Database::getConnection();
    echo "<div class='success'>✅ Database connection successful</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🔍 Step 1: Check Recent Contact Submissions</h2>";
    
    $stmt = $db->prepare("
        SELECT email, created_at, is_newsletter_signup 
        FROM contact_submissions 
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 2 HOUR) 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recent_submissions = $stmt->fetchAll();
    
    if ($recent_submissions) {
        echo "<div class='info'>📋 Recent contact submissions (last 2 hours):</div>";
        echo "<pre>";
        foreach ($recent_submissions as $sub) {
            $type = $sub['is_newsletter_signup'] ? 'NEWSLETTER' : 'CONTACT';
            echo "{$sub['email']} - $type - {$sub['created_at']}\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='info'>ℹ️ No recent contact submissions found</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🧪 Step 2: Test Contact Form Handler Directly</h2>";
    
    $test_email = 'debug-contact-popup-' . time() . '@example.com';
    
    $test_data = [
        'name' => 'Debug Contact Test',
        'email' => $test_email,
        'phone' => '555-0123',
        'service' => 'Testing',
        'message' => 'This is a debug test for contact form popup.'
    ];
    
    echo "<div class='info'>🔄 Testing handleContactForm with email: $test_email</div>";
    
    $result = handleContactForm($test_data);
    
    echo "<div class='info'>📤 handleContactForm result:</div>";
    echo "<pre>";
    echo "Success: " . ($result['success'] ? 'TRUE' : 'FALSE') . "\n";
    echo "Message: '{$result['message']}'\n";
    echo "</pre>";
    
    if ($result['success']) {
        echo "<div class='success'>✅ Contact form handler working correctly</div>";
        
        // Simulate the contact.php variables
        $form_message = $result['message'];
        $form_status = 'success';
        
        echo "<div class='info'>📝 Simulated contact.php variables:</div>";
        echo "<pre>";
        echo "\$form_message = '{$form_message}'\n";
        echo "\$form_status = '{$form_status}'\n";
        echo "</pre>";
        
        // Show what the popup HTML would look like
        echo "<div class='info'>🎨 Generated popup HTML:</div>";
        echo "<pre>";
        echo htmlspecialchars("
<div class=\"contact-popup-overlay\" id=\"contactPopupOverlay\"></div>
<div class=\"contact-popup-{$form_status}\" id=\"contactPopupMessage\">
    <div class=\"popup-icon\">✅</div>
    <div class=\"popup-title\">Message Sent!</div>
    <div class=\"popup-message\">{$form_message}</div>
    <div class=\"popup-close-hint\">Click anywhere to close</div>
</div>
        ");
        echo "</pre>";
        
    } else {
        echo "<div class='error'>❌ Contact form handler failed: {$result['message']}</div>";
        
        // Check if it's a duplicate submission issue
        if (strpos($result['message'], 'already submitted') !== false) {
            echo "<div class='warning'>⚠️ This appears to be a duplicate submission issue</div>";
            
            // Check recent submissions for this email pattern
            $stmt = $db->prepare("
                SELECT email, created_at 
                FROM contact_submissions 
                WHERE email LIKE ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                ORDER BY created_at DESC
            ");
            $stmt->execute(['%contact-popup-test%']);
            $duplicates = $stmt->fetchAll();
            
            if ($duplicates) {
                echo "<div class='info'>📋 Found recent submissions with similar email:</div>";
                echo "<pre>";
                foreach ($duplicates as $dup) {
                    echo "{$dup['email']} - {$dup['created_at']}\n";
                }
                echo "</pre>";
            }
        }
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🎯 Step 3: Test with Completely Fresh Email</h2>";
    
    $fresh_email = 'fresh-contact-' . uniqid() . '@example.com';
    
    $fresh_data = [
        'name' => 'Fresh Contact Test',
        'email' => $fresh_email,
        'phone' => '555-9999',
        'service' => 'Fresh Testing',
        'message' => 'This is a fresh test with unique email.'
    ];
    
    echo "<div class='info'>🔄 Testing with completely fresh email: $fresh_email</div>";
    
    $fresh_result = handleContactForm($fresh_data);
    
    if ($fresh_result['success']) {
        echo "<div class='success'>✅ Fresh email test successful!</div>";
        echo "<div class='info'>📝 Success message: '{$fresh_result['message']}'</div>";
        
        // Show the actual popup that would be rendered
        echo "<h3>🎨 ACTUAL POPUP PREVIEW:</h3>";
        echo "<div style='position:relative; background:#f8f9fa; padding:20px; border-radius:8px; margin:20px 0;'>";
        echo "<div style='position:absolute; top:0; left:0; right:0; bottom:0; background:rgba(0,0,0,0.7); z-index:1;'></div>";
        echo "<div style='position:relative; z-index:2; background:linear-gradient(135deg, #28a745 0%, #20c997 100%); color:white; padding:40px; border-radius:20px; text-align:center; max-width:400px; margin:0 auto; box-shadow:0 15px 40px rgba(0,0,0,0.4);'>";
        echo "<div style='font-size:48px; margin-bottom:15px;'>✅</div>";
        echo "<div style='font-size:28px; font-weight:700; margin-bottom:15px;'>Message Sent!</div>";
        echo "<div style='font-size:18px; margin-bottom:20px;'>" . htmlspecialchars($fresh_result['message']) . "</div>";
        echo "<div style='font-size:14px; opacity:0.8; font-style:italic;'>Click anywhere to close</div>";
        echo "</div>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>❌ Fresh email test failed: {$fresh_result['message']}</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<div class='test-section'>";
echo "<h2>🎯 DIAGNOSIS</h2>";
echo "<div class='info'>
<strong>Contact Form Popup Issues:</strong><br><br>

<strong>1. If handleContactForm is failing:</strong><br>
- Check for duplicate submission blocking (1-hour limit)<br>
- Use completely unique email addresses for testing<br>
- Verify all required fields are provided<br><br>

<strong>2. If handleContactForm works but popup doesn't show:</strong><br>
- Check if \$form_message is being set in contact.php<br>
- Verify popup HTML is being rendered<br>
- Check JavaScript console for errors<br>
- Ensure CSS styles are not conflicting<br><br>

<strong>3. Solutions:</strong><br>
- Test with fresh, unique email addresses<br>
- Check browser developer tools for JavaScript errors<br>
- Verify popup elements exist in DOM<br>
- Test popup CSS and JavaScript separately<br>
</div>";
echo "</div>";
?>
