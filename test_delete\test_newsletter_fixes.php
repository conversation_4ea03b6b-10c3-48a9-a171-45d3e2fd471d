<?php
/**
 * Test Newsletter Fixes
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h1>Newsletter System Test</h1>\n";

try {
    $db = Database::getConnection();
    
    echo "<h2>✅ Newsletter System Test Results</h2>\n";
    
    // Check contact_submissions with newsletter flag
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN is_newsletter_signup = 1 THEN 1 ELSE 0 END) as newsletter_count,
            SUM(CASE WHEN is_newsletter_signup = 0 OR is_newsletter_signup IS NULL THEN 1 ELSE 0 END) as contact_count
        FROM contact_submissions
    ");
    $stmt->execute();
    $stats = $stmt->fetch();
    
    echo "📊 Contact Submissions Stats:\n";
    echo "   - Total Submissions: {$stats['total']}\n";
    echo "   - Newsletter Signups: {$stats['newsletter_count']}\n";
    echo "   - Contact Forms: {$stats['contact_count']}\n";
    
    // Check newsletter_subscribers
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM newsletter_subscribers");
    $stmt->execute();
    $newsletter_total = $stmt->fetch()['count'];
    echo "   - Newsletter Subscribers Table: $newsletter_total\n\n";
    
    // Check hero CTA settings
    echo "🎯 Hero CTA Newsletter Settings:\n";
    $stmt = $db->prepare("
        SELECT page_name, newsletter_success_message 
        FROM hero_sections 
        WHERE show_newsletter_input = 1 
        ORDER BY page_name
    ");
    $stmt->execute();
    $hero_ctaes = $stmt->fetchAll();
    
    foreach ($hero_ctaes as $cta) {
        echo "   - {$cta['page_name']}: '{$cta['newsletter_success_message']}'\n";
    }
    
    echo "\n✅ FIXES IMPLEMENTED:\n";
    echo "1. Newsletter signups now appear in admin/contacts with 'Newsletter' type badge\n";
    echo "2. Hero CTA success messages use database-configured text\n";
    echo "3. Newsletter-signup.php stores in both newsletter_subscribers and contact_submissions\n";
    echo "4. Admin contacts page shows newsletter vs contact form breakdown\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
