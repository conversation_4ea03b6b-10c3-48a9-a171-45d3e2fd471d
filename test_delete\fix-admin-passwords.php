<?php
/**
 * Fix Admin Passwords - Convert to Proper Bcrypt Hashes
 * This script fixes the password hash format issue
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 Fix Admin Password Hashes</h2>\n";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=monolith_design;charset=utf8', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Database connected successfully</p>\n";
    
    // Check current password hashes
    echo "<h3>🔍 Current Password Hashes</h3>\n";
    $stmt = $pdo->query("SELECT username, password FROM admin_users");
    $users = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>Username</th>";
    echo "<th style='padding: 10px;'>Current Hash</th>";
    echo "<th style='padding: 10px;'>Hash Type</th>";
    echo "</tr>\n";
    
    foreach ($users as $user) {
        $hash_type = 'Unknown';
        if (strlen($user['password']) === 32 && ctype_xdigit($user['password'])) {
            $hash_type = 'MD5 (Invalid)';
        } elseif (strpos($user['password'], '$2y$') === 0) {
            $hash_type = 'Bcrypt (Valid)';
        } elseif (strpos($user['password'], '$2a$') === 0) {
            $hash_type = 'Bcrypt (Valid)';
        }
        
        $color = ($hash_type === 'MD5 (Invalid)') ? 'red' : 'green';
        
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>{$user['username']}</strong></td>";
        echo "<td style='padding: 10px; font-family: monospace; font-size: 12px;'>" . substr($user['password'], 0, 50) . "...</td>";
        echo "<td style='padding: 10px; color: {$color};'><strong>{$hash_type}</strong></td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Fix the passwords
    echo "<h3>🔧 Fixing Password Hashes</h3>\n";
    
    // Default passwords to set
    $default_passwords = [
        'admin' => 'admin123',
        'contact_admin' => 'admin123', 
        'support' => 'admin123',
        'test_user' => 'admin123'
    ];
    
    $stmt = $pdo->prepare("UPDATE admin_users SET password = ? WHERE username = ?");
    
    foreach ($default_passwords as $username => $password) {
        // Generate proper bcrypt hash
        $bcrypt_hash = password_hash($password, PASSWORD_DEFAULT);
        
        if ($stmt->execute([$bcrypt_hash, $username])) {
            echo "<p style='color: green;'>✓ Fixed password hash for user: <strong>{$username}</strong></p>\n";
            echo "<p style='margin-left: 20px; font-size: 12px; color: #666;'>New hash: " . substr($bcrypt_hash, 0, 30) . "...</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Failed to update password for user: {$username}</p>\n";
        }
    }
    
    echo "<h3>✅ Verification</h3>\n";
    
    // Verify the fixes worked
    $stmt = $pdo->prepare("SELECT username, password FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $admin_user = $stmt->fetch();
    
    if ($admin_user) {
        $test_password = 'admin123';
        $is_valid = password_verify($test_password, $admin_user['password']);
        
        if ($is_valid) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #155724;'>🎉 SUCCESS!</h4>\n";
            echo "<p>Password verification is now working correctly.</p>\n";
            echo "<p><strong>Login Credentials:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Username: <code>admin</code></li>\n";
            echo "<li>Password: <code>admin123</code></li>\n";
            echo "</ul>\n";
            echo "<p><a href='../admin/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>→ Try Login Now</a></p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #721c24;'>❌ Still Not Working</h4>\n";
            echo "<p>Password verification is still failing. There may be another issue.</p>\n";
            echo "</div>\n";
        }
    }
    
    echo "<h3>📋 Updated User List</h3>\n";
    
    // Show all users with their new status
    $stmt = $pdo->query("SELECT username, email, role, status FROM admin_users ORDER BY id");
    $users = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>Username</th>";
    echo "<th style='padding: 10px;'>Email</th>";
    echo "<th style='padding: 10px;'>Role</th>";
    echo "<th style='padding: 10px;'>Status</th>";
    echo "<th style='padding: 10px;'>Default Password</th>";
    echo "</tr>\n";
    
    foreach ($users as $user) {
        $statusColor = $user['status'] === 'active' ? 'green' : 'red';
        $roleColor = $user['role'] === 'super_admin' ? 'blue' : 'orange';
        $default_pwd = isset($default_passwords[$user['username']]) ? $default_passwords[$user['username']] : 'N/A';
        
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>{$user['username']}</strong></td>";
        echo "<td style='padding: 10px;'>{$user['email']}</td>";
        echo "<td style='padding: 10px; color: {$roleColor};'><strong>{$user['role']}</strong></td>";
        echo "<td style='padding: 10px; color: {$statusColor};'><strong>{$user['status']}</strong></td>";
        echo "<td style='padding: 10px;'><code>{$default_pwd}</code></td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h4>⚠️ Important Security Notes</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Change passwords immediately</strong> - These are temporary default passwords</li>\n";
    echo "<li><strong>Use strong passwords</strong> - At least 8 characters with mixed case, numbers, and symbols</li>\n";
    echo "<li><strong>Regular updates</strong> - Change passwords every 90 days</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Stack trace:</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
