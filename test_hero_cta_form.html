<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero CTA Newsletter Test</title>
    <style>
        body { font-family: <PERSON><PERSON>, sans-serif; padding: 20px; }
        .hero-newsletter-form { margin: 20px 0; }
        .newsletter-input-group { display: flex; gap: 10px; margin-bottom: 10px; }
        .newsletter-input { padding: 10px; border: 1px solid #ccc; flex: 1; }
        .newsletter-button { padding: 10px 20px; background: #2D5A27; color: white; border: none; cursor: pointer; }
        .newsletter-success-message { 
            background: #d4edda; 
            color: #155724; 
            padding: 15px; 
            border: 1px solid #c3e6cb; 
            border-radius: 4px; 
            margin-top: 10px;
        }
        .hidden { display: none !important; }
    </style>
</head>
<body>
    <h1>Hero CTA Newsletter Test</h1>
    
    <form class="hero-newsletter-form" id="heroNewsletterForm" action="/monolith-design/newsletter-signup.php" method="POST">
        <div class="newsletter-input-group">
            <input type="email" 
                   name="email" 
                   class="newsletter-input" 
                   placeholder="Enter your email" 
                   required>
            <button type="submit" class="newsletter-button">Subscribe</button>
        </div>
        
        <div class="newsletter-success-message" id="heroNewsletterSuccessMessage" style="display: none;">
            <div class="success-text">Thank you for subscribing!</div>
        </div>
        
        <input type="hidden" name="source" value="hero_cta">
        <input type="hidden" name="page" value="test">
    </form>

    <div id="debug-output" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6;"></div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('heroNewsletterForm');
        const successMessage = document.getElementById('heroNewsletterSuccessMessage');
        const debugOutput = document.getElementById('debug-output');

        function log(message) {
            debugOutput.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
        }

        log('Hero CTA newsletter script loaded');

        if (form) {
            log('Form found: ' + form.id);
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                log('Form submitted, preventDefault called');

                const email = this.querySelector('input[type="email"]').value;
                const formData = new FormData(this);
                
                log('Email: ' + email);
                log('Form action: ' + this.action);

                if (email) {
                    log('Sending AJAX request...');
                    
                    // Send AJAX request to newsletter signup
                    fetch(this.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        log('Response received. Status: ' + response.status);
                        return response.json();
                    })
                    .then(data => {
                        log('JSON parsed: ' + JSON.stringify(data));
                        
                        if (data.success) {
                            log('Success! Showing message: ' + data.message);
                            
                            // Update success message with response message
                            const successText = successMessage.querySelector('.success-text');
                            if (successText) {
                                successText.textContent = data.message;
                                log('Success text updated');
                            }
                            
                            // Hide form and show success message
                            this.style.display = 'none';
                            successMessage.style.display = 'block';
                            log('Form hidden, success message shown');

                            // Reset form after 5 seconds
                            setTimeout(() => {
                                log('Resetting form display');
                                this.style.display = 'block';
                                successMessage.style.display = 'none';
                                this.reset();
                            }, 5000);
                        } else {
                            log('Error from server: ' + data.message);
                            alert(data.message || 'An error occurred. Please try again.');
                        }
                    })
                    .catch(error => {
                        log('Fetch error: ' + error.toString());
                        console.error('Newsletter signup error:', error);
                        alert('An error occurred. Please try again.');
                    });
                } else {
                    log('No email provided');
                }
            });
        } else {
            log('Form NOT found!');
        }
    });
    </script>
</body>
</html>
