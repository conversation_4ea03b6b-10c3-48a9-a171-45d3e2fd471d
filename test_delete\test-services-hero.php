<?php
/**
 * Test Services Hero Header
 */

define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

echo "Testing Services Hero Header\n";
echo "============================\n\n";

try {
    $db = Database::getConnection();
    
    // Test 1: Check if services hero header exists
    echo "1. Checking for services hero header...\n";
    $hero = getHeroHeader('services');
    
    if ($hero) {
        echo "✅ Services hero header found!\n";
        echo "   - Title: " . $hero['page_title'] . "\n";
        echo "   - Subtitle: " . $hero['subtitle'] . "\n";
        echo "   - Background Type: " . $hero['background_type'] . "\n";
        echo "   - Active: " . ($hero['active'] ? 'Yes' : 'No') . "\n";
    } else {
        echo "❌ Services hero header not found. Creating one...\n";
        
        // Create the hero header
        $stmt = $db->prepare("
            INSERT INTO hero_headers (
                page_name, page_title, subtitle, show_breadcrumbs,
                background_type, background_image, background_gradient, background_color, background_opacity,
                height_type, height_custom, padding_top, padding_bottom,
                title_color, subtitle_color, breadcrumb_color,
                show_cta_button, active
            ) VALUES (
                'services', 'Our Services', 'Comprehensive Architectural & Construction Solutions', 1,
                'image', 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1600&h=900&fit=crop', 
                'linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(230, 126, 34, 0.6) 100%)', 
                '#1A1A1A', 0.7, 'medium', 400, '4rem', '4rem',
                '#ffffff', '#ffffff', '#ffffff', 0, 1
            )
        ");
        
        if ($stmt->execute()) {
            echo "✅ Services hero header created successfully!\n";
        } else {
            echo "❌ Failed to create services hero header\n";
        }
    }
    
    echo "\n2. Testing hero header retrieval...\n";
    $hero = getHeroHeader('services');
    if ($hero) {
        echo "✅ Hero header retrieval working\n";
        echo "   - Page Name: " . $hero['page_name'] . "\n";
        echo "   - Title: " . $hero['page_title'] . "\n";
    } else {
        echo "❌ Hero header retrieval failed\n";
    }
    
    echo "\n🎉 Services page hero header setup complete!\n";
    echo "Visit /services.php to see the dynamic hero in action.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
