<?php
/**
 * Test Contact Form Success Alert Issues
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h1>🔍 CONTACT FORM SUCCESS ALERT DEBUG</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:10px;border-radius:4px;margin:5px 0;} 
.error{color:red;background:#f8d7da;padding:10px;border-radius:4px;margin:5px 0;} 
.info{color:blue;background:#d1ecf1;padding:10px;border-radius:4px;margin:5px 0;} 
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:5px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow-x:auto;}
.form-message{padding:1rem 1.5rem;border-radius:8px;margin:1rem 0;font-weight:500;}
.form-message.success{background-color:#d4edda;color:#155724;border:1px solid #c3e6cb;}
.form-message.error{background-color:#f8d7da;color:#721c24;border:1px solid #f5c6cb;}
</style>";

try {
    echo "<h2>🧪 Step 1: Test handleContactForm Function</h2>";
    
    // Test the contact form handler directly
    $test_data = [
        'name' => 'Test User - ' . date('Y-m-d H:i:s'),
        'email' => 'test-contact-alert-' . time() . '@example.com',
        'phone' => '************',
        'service' => 'Test Service',
        'message' => 'This is a test message to verify the contact form success alert functionality.'
    ];
    
    echo "<div class='info'>📝 Testing with data:</div>";
    echo "<pre>" . print_r($test_data, true) . "</pre>";
    
    $result = handleContactForm($test_data);
    
    echo "<div class='info'>📊 Function Result:</div>";
    echo "<pre>" . print_r($result, true) . "</pre>";
    
    if ($result['success']) {
        echo "<div class='success'>✅ handleContactForm returned SUCCESS</div>";
        echo "<div class='success'>✅ Success message: '{$result['message']}'</div>";
    } else {
        echo "<div class='error'>❌ handleContactForm returned FAILURE</div>";
        echo "<div class='error'>❌ Error message: '{$result['message']}'</div>";
    }
    
    echo "<h2>🎨 Step 2: Test Message Display HTML</h2>";
    
    // Simulate the contact.php form message display
    $form_message = $result['message'];
    $form_status = $result['success'] ? 'success' : 'error';
    
    echo "<div class='info'>📋 Simulating contact.php message display:</div>";
    echo "<div class='info'>📝 form_message = '{$form_message}'</div>";
    echo "<div class='info'>📝 form_status = '{$form_status}'</div>";
    
    if ($form_message) {
        echo "<div class='warning'>🧪 This is how the message should appear in contact.php:</div>";
        echo "<div class='form-message {$form_status}'>";
        echo htmlspecialchars($form_message);
        echo "</div>";
    }
    
    echo "<h2>🔍 Step 3: Check CSS Loading</h2>";
    
    // Check if arkify-style.css exists and contains the form message styles
    $css_path = __DIR__ . '/assets/css/arkify-style.css';
    if (file_exists($css_path)) {
        echo "<div class='success'>✅ arkify-style.css found</div>";
        
        $css_content = file_get_contents($css_path);
        if (strpos($css_content, '.form-message.success') !== false) {
            echo "<div class='success'>✅ .form-message.success styles found in CSS</div>";
        } else {
            echo "<div class='error'>❌ .form-message.success styles NOT found in CSS</div>";
        }
        
        if (strpos($css_content, '.form-message.error') !== false) {
            echo "<div class='success'>✅ .form-message.error styles found in CSS</div>";
        } else {
            echo "<div class='error'>❌ .form-message.error styles NOT found in CSS</div>";
        }
    } else {
        echo "<div class='error'>❌ arkify-style.css NOT found at: {$css_path}</div>";
    }
    
    echo "<h2>🌐 Step 4: Check Contact.php Integration</h2>";
    
    // Check if contact.php properly handles the form submission
    $contact_php_path = __DIR__ . '/contact.php';
    if (file_exists($contact_php_path)) {
        echo "<div class='success'>✅ contact.php found</div>";
        
        $contact_content = file_get_contents($contact_php_path);
        
        // Check for form processing
        if (strpos($contact_content, 'handleContactForm($_POST)') !== false) {
            echo "<div class='success'>✅ handleContactForm call found in contact.php</div>";
        } else {
            echo "<div class='error'>❌ handleContactForm call NOT found in contact.php</div>";
        }
        
        // Check for message display
        if (strpos($contact_content, 'form-message') !== false) {
            echo "<div class='success'>✅ form-message display code found in contact.php</div>";
        } else {
            echo "<div class='error'>❌ form-message display code NOT found in contact.php</div>";
        }
        
        // Check for form_status assignment
        if (strpos($contact_content, "form_status = \$result['success'] ? 'success' : 'error'") !== false) {
            echo "<div class='success'>✅ Correct form_status assignment found</div>";
        } else {
            echo "<div class='error'>❌ Correct form_status assignment NOT found</div>";
        }
    } else {
        echo "<div class='error'>❌ contact.php NOT found</div>";
    }
    
    echo "<h2>🎯 Step 5: Recommendations</h2>";
    
    if ($result['success']) {
        echo "<div class='info'>📝 The backend functionality appears to be working correctly.</div>";
        echo "<div class='info'>📝 The issue might be in the frontend display or JavaScript.</div>";
        echo "<div class='warning'>🔍 Check if any JavaScript is interfering with form submission or message display.</div>";
        echo "<div class='warning'>🔍 Verify that the contact form is being submitted properly via POST.</div>";
        echo "<div class='warning'>🔍 Check browser developer tools for any CSS conflicts hiding the success message.</div>";
    } else {
        echo "<div class='error'>❌ Backend functionality has issues that need to be resolved first.</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ TEST FAILED: " . $e->getMessage() . "</div>";
    echo "<div class='error'>📍 Stack trace:</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<div class='info'>💡 To test this manually:</div>";
echo "<div class='info'>1. Go to http://localhost/monolith-design/contact</div>";
echo "<div class='info'>2. Fill out the contact form</div>";
echo "<div class='info'>3. Submit the form</div>";
echo "<div class='info'>4. Check if a success message appears above the form</div>";
?>
