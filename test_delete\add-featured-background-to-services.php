<?php
/**
 * Migration: Add featured_background column to services table
 * Run this once to add the new column for service-specific hero backgrounds
 */

define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

try {
    $db = Database::getConnection();
    
    // Check if column already exists
    $stmt = $db->prepare("SHOW COLUMNS FROM services LIKE 'featured_background'");
    $stmt->execute();
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        // Add the featured_background column
        $sql = "ALTER TABLE services ADD COLUMN featured_background VARCHAR(500) DEFAULT '' AFTER icon";
        $db->exec($sql);
        echo "✅ Successfully added featured_background column to services table\n";
    } else {
        echo "ℹ️ featured_background column already exists in services table\n";
    }
    
    // Also add hero_title and hero_subtitle columns for service-specific titles
    $stmt = $db->prepare("SHOW COLUMNS FROM services LIKE 'hero_title'");
    $stmt->execute();
    $hero_title_exists = $stmt->fetch();
    
    if (!$hero_title_exists) {
        $sql = "ALTER TABLE services ADD COLUMN hero_title VARCHAR(255) DEFAULT '' AFTER featured_background";
        $db->exec($sql);
        echo "✅ Successfully added hero_title column to services table\n";
    } else {
        echo "ℹ️ hero_title column already exists in services table\n";
    }
    
    $stmt = $db->prepare("SHOW COLUMNS FROM services LIKE 'hero_subtitle'");
    $stmt->execute();
    $hero_subtitle_exists = $stmt->fetch();
    
    if (!$hero_subtitle_exists) {
        $sql = "ALTER TABLE services ADD COLUMN hero_subtitle VARCHAR(255) DEFAULT '' AFTER hero_title";
        $db->exec($sql);
        echo "✅ Successfully added hero_subtitle column to services table\n";
    } else {
        echo "ℹ️ hero_subtitle column already exists in services table\n";
    }
    
    echo "\n🎉 Migration completed successfully!\n";
    echo "Services table now supports:\n";
    echo "- featured_background: Service-specific hero background images\n";
    echo "- hero_title: Custom hero title for each service\n";
    echo "- hero_subtitle: Custom hero subtitle for each service\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
