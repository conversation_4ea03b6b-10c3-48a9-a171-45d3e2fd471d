<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero CTA Success Message Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }
        
        /* Hero CTA Styles */
        .hero-newsletter-form { max-width: 450px; margin: 20px auto; }
        .newsletter-input-group { display: flex; gap: 0; background: rgba(0, 0, 0, 0.1); border-radius: 50px; padding: 8px; }
        .newsletter-hero-input { flex: 1; padding: 12px 20px; border: none; background: transparent; font-size: 16px; border-radius: 50px; outline: none; }
        .newsletter-hero-button { padding: 12px 24px; background: #007cba; color: white; border: none; border-radius: 50px; cursor: pointer; font-weight: 600; }
        .newsletter-hero-button:hover { background: #005a87; }
        
        .newsletter-success-message {
            margin-top: 1rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 12px;
            font-weight: 600;
            text-align: center;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            animation: successFadeIn 0.5s ease-out;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .success-icon { font-size: 1.2em; animation: successBounce 0.6s ease-out; }
        .success-text { font-size: 1.1em; }
        
        @keyframes successFadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes successBounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-5px); }
            60% { transform: translateY(-3px); }
        }
        
        .log { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 4px; font-family: monospace; margin: 10px 0; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Hero CTA Success Message Test</h1>
        
        <div class="test-section">
            <h2>📧 Test Hero Newsletter Form</h2>
            <p>This test replicates the exact hero CTA newsletter form structure and JavaScript from the templates/hero-cta.php file.</p>
            
            <form class="hero-newsletter-form" id="heroNewsletterForm" method="POST" action="/monolith-design/newsletter-signup.php">
                <div class="newsletter-input-group">
                    <input type="email"
                           name="email"
                           class="newsletter-hero-input"
                           placeholder="Enter your email address"
                           required>
                    <button type="submit" class="newsletter-hero-button">
                        Subscribe
                    </button>
                </div>
                <div class="newsletter-success-message" id="heroNewsletterSuccessMessage" style="display: none;">
                    <div class="success-icon">✅</div>
                    <div class="success-text">Thank you for subscribing!</div>
                </div>
                <input type="hidden" name="source" value="hero_cta">
                <input type="hidden" name="page" value="test">
            </form>
        </div>
        
        <div class="test-section">
            <h2>📋 Test Log</h2>
            <div id="testLog" class="log">Ready to test...\n</div>
        </div>
        
        <div class="test-section">
            <h2>🔍 Debug Information</h2>
            <div id="debugInfo" class="info">
                <strong>Form Elements:</strong><br>
                - Form ID: <span id="formCheck">Checking...</span><br>
                - Success Message ID: <span id="successCheck">Checking...</span><br>
                - Success Text Element: <span id="textCheck">Checking...</span><br>
            </div>
        </div>
    </div>

    <script>
    function log(message) {
        const logDiv = document.getElementById('testLog');
        const timestamp = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${timestamp}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
        console.log(`[Hero CTA Test] ${message}`);
    }

    document.addEventListener('DOMContentLoaded', function() {
        log('DOM loaded, initializing test...');
        
        // Check form elements
        const form = document.getElementById('heroNewsletterForm');
        const successMessage = document.getElementById('heroNewsletterSuccessMessage');
        const successText = successMessage ? successMessage.querySelector('.success-text') : null;
        
        document.getElementById('formCheck').textContent = form ? '✅ Found' : '❌ Missing';
        document.getElementById('successCheck').textContent = successMessage ? '✅ Found' : '❌ Missing';
        document.getElementById('textCheck').textContent = successText ? '✅ Found' : '❌ Missing';
        
        if (!form) {
            log('❌ ERROR: Hero newsletter form not found!');
            return;
        }
        
        if (!successMessage) {
            log('❌ ERROR: Success message element not found!');
            return;
        }
        
        log('✅ All required elements found, setting up event listener...');
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            log('📝 Form submitted, preventDefault called');

            const email = this.querySelector('input[type="email"]').value;
            const sourceInput = this.querySelector('input[name="source"]');
            const pageInput = this.querySelector('input[name="page"]');
            
            log(`📧 Email: ${email}`);
            log(`📍 Source: ${sourceInput ? sourceInput.value : 'unknown'}`);
            log(`📄 Page: ${pageInput ? pageInput.value : 'unknown'}`);

            if (email) {
                // Add loading state to button
                const button = this.querySelector('button[type="submit"]');
                const originalText = button.innerHTML;
                button.innerHTML = 'Subscribing...';
                button.disabled = true;
                log('🔄 Button set to loading state');

                // Create FormData manually to ensure proper formatting
                const formData = new FormData();
                formData.append('email', email);
                formData.append('source', sourceInput ? sourceInput.value : 'hero_cta');
                formData.append('page', pageInput ? pageInput.value : 'test');

                log(`🚀 Sending POST request to: ${this.action}`);

                // Send AJAX request to newsletter signup
                fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    log(`📥 Response received. Status: ${response.status}`);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    log(`📄 Raw response: ${text.substring(0, 200)}...`);
                    
                    let data;
                    try {
                        data = JSON.parse(text);
                    } catch (parseError) {
                        log(`❌ JSON parse error: ${parseError.message}`);
                        throw new Error('Invalid JSON response');
                    }
                    
                    log(`📊 Parsed response: ${JSON.stringify(data)}`);
                    
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                    log('🔄 Button restored to normal state');

                    if (data.success) {
                        // Update success message with database content or response message
                        if (successText) {
                            successText.textContent = data.message || 'Thank you for subscribing!';
                            log(`✅ Success text updated: "${successText.textContent}"`);
                        }
                        
                        log('🎉 SUCCESS! Hiding form and showing success message...');
                        
                        // Hide form and show success message
                        this.style.display = 'none';
                        successMessage.style.display = 'block';
                        log('👁️ Form hidden, success message shown');

                        // Reset form after 5 seconds
                        setTimeout(() => {
                            log('🔄 Timeout reached, resetting form display...');
                            this.style.display = 'block';
                            successMessage.style.display = 'none';
                            this.reset();
                            log('✅ Form reset and ready for next test');
                        }, 5000);
                    } else {
                        // Show error message
                        log(`❌ Error response: ${data.message}`);
                        alert(data.message || 'An error occurred. Please try again.');
                    }
                })
                .catch(error => {
                    log(`❌ Fetch error: ${error.message}`);
                    
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                    
                    alert('An error occurred. Please try again.');
                });
            } else {
                log('❌ No email provided');
            }
        });
        
        log('✅ Event listener attached successfully');
    });
    </script>
</body>
</html>
