<?php
/**
 * Test Footer Newsletter Functionality
 */

// Set up environment for newsletter test
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
$_POST = [
    'email' => 'footer-test-' . time() . '@example.com',
    'source' => 'brand_footer',
    'page' => 'test'
];

echo "Testing Footer Newsletter Functionality\n";
echo "======================================\n\n";

// Capture output
ob_start();
include 'newsletter-signup.php';
$footer_response = ob_get_clean();

echo "Footer Newsletter Response:\n";
echo $footer_response . "\n\n";

$footer_data = json_decode($footer_response, true);
if ($footer_data && $footer_data['success']) {
    echo "✅ Footer newsletter signup working correctly\n";
    echo "✅ Success message: " . $footer_data['message'] . "\n";
} else {
    echo "❌ Footer newsletter signup failed\n";
    if ($footer_data) {
        echo "❌ Error: " . $footer_data['message'] . "\n";
    }
}
?>
