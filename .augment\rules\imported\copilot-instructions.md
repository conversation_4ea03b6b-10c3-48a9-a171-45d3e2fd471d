---
type: "agent_requested"
description: "Example description"
---
# Copilot Instructions for Monolith Design Co. Codebase

## Overview
This is a professional PHP website theme for engineering and architecture firms. It features a modular structure, a comprehensive admin dashboard, and a database-driven content management system. The codebase is designed for easy customization and robust security.

## Architecture & Key Components
- **Root Pages**: Main site pages (e.g., `index.php`, `about.php`, `services.php`, `projects.php`, `contact.php`, etc.)
- **Admin Panel**: Located in `/admin/`, includes content management for services, projects, team, blog, and site options.
- **Templates**: Shared UI components in `/templates/` (e.g., `header.php`, `footer.php`, `hero-cta.php`).
- **Assets**: Static files in `/assets/` (CSS, JS, images).
- **Includes**: Helper scripts in `/includes/` (e.g., `functions.php`, `advanced-slider-helpers.php`).
- **Config**: Central configuration in `config.php` (site settings, DB credentials, security, theme options).

## Developer Workflows
- **Setup**: 
  1. Upload files to server.
  2. Create MySQL database.
  3. Visit `install.php` to initialize DB.
  4. Delete `install.php` after setup.
  5. Access admin at `/admin/`.
- **Theme Customization**: Update theme options via admin or directly in `config.php` and `/templates/`.
- **Database**: Structure defined in `test_delete/database.sql`. All content is managed via MySQL.
- **File Uploads**: Images are uploaded to `/assets/images/uploads/` (auto-created if missing).
- **Security**: CSRF tokens, session hardening, and security headers are enforced in `config.php`. All DB queries should use prepared statements.
- **Error Reporting**: Controlled by environment detection in `config.php` (production disables display, logs to `error.log`).

## Project-Specific Conventions
- **Clean URLs**: SEO-friendly, typically routed via `.htaccess` (no `.php` extensions in public URLs).
- **Theme Options**: Default values in `config.php`, overridden by DB values via admin.
- **Responsive Design**: CSS in `/assets/css/` follows an 8-point grid and mobile-first approach.
- **Typography**: Uses Montserrat and Lato via Google Fonts.
- **Social & Newsletter**: Social links and newsletter settings are managed in theme options and templates.

## Integration Points
- **External Services**: SMTP email (configurable in `config.php`), Google Maps (contact page), Google Fonts.
- **Admin Panel**: All site content, team, projects, and services are managed via `/admin/` scripts.

## Examples
- To add a new service: Use `/admin/services.php` or update DB directly.
- To change site logo: Update via admin or replace file in `/assets/images/`.
- To customize footer: Edit `/templates/footer.php` and theme options in admin.

## References
- For setup: `INSTALLATION.txt`, `test_delete/README.md`, `test_delete/documentation.html`
- For DB schema: `test_delete/database.sql`
- For changelog: `test_delete/Changelog.txt`

---
**Agents should follow existing patterns for security, modularity, and maintainability. When in doubt, reference the admin panel and config files for conventions.**
