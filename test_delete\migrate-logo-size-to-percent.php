<?php
/**
 * Migration Script: Convert Logo Size from Text to Percentage
 * 
 * This script converts the old logo_size system (small/medium/large) 
 * to the new percentage-based system (logo_size_percent).
 * 
 * Conversion mapping:
 * - small  -> 75%
 * - medium -> 100% (default)
 * - large  -> 150%
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h2>Logo Size Migration Script</h2>\n";
echo "<p>Converting logo_size from text values to percentage values...</p>\n";

try {
    // Get current logo_size value
    $currentLogoSize = getThemeOption('logo_size', 'medium');
    echo "<p><strong>Current logo_size:</strong> " . htmlspecialchars($currentLogoSize) . "</p>\n";
    
    // Check if already migrated
    $currentPercent = getThemeOption('logo_size_percent', null);
    if ($currentPercent !== null) {
        echo "<p><strong>Already migrated!</strong> Current logo_size_percent: {$currentPercent}%</p>\n";
        echo "<p>If you want to re-run the migration, delete the logo_size_percent option first.</p>\n";
        exit;
    }
    
    // Conversion mapping
    $conversionMap = [
        'small' => 75,
        'medium' => 100,
        'large' => 150
    ];
    
    // Convert to percentage
    $newPercentage = $conversionMap[$currentLogoSize] ?? 100;
    
    echo "<p><strong>Converting to:</strong> {$newPercentage}%</p>\n";
    
    // Update database
    updateThemeOption('logo_size_percent', $newPercentage);
    
    // Verify the update
    $verifyPercent = getThemeOption('logo_size_percent');
    if ($verifyPercent == $newPercentage) {
        echo "<p style='color: green;'><strong>✓ Migration successful!</strong></p>\n";
        echo "<p>New logo_size_percent value: {$verifyPercent}%</p>\n";
        
        // Optional: Keep old value for rollback purposes
        echo "<p><em>Note: The old logo_size value has been kept for rollback purposes.</em></p>\n";
        
    } else {
        echo "<p style='color: red;'><strong>✗ Migration failed!</strong></p>\n";
        echo "<p>Expected: {$newPercentage}%, Got: {$verifyPercent}</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<h3>Migration Summary</h3>\n";
echo "<ul>\n";
echo "<li>Old system: logo_size (small/medium/large)</li>\n";
echo "<li>New system: logo_size_percent (50-200)</li>\n";
echo "<li>Admin interface now uses percentage slider</li>\n";
echo "<li>Frontend uses CSS custom properties for responsive scaling</li>\n";
echo "</ul>\n";

echo "<p><a href='../admin/'>← Back to Admin</a></p>\n";
?>
