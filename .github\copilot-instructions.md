
# Copilot Instructions for Monolith Design Co. Codebase

## Overview
This is a modular, secure PHP website theme for engineering and architecture firms. It features a database-driven CMS, a comprehensive admin dashboard, and a strong focus on maintainability and security. See [test_delete/README.md](test_delete/README.md) for product scope and features.

## Architecture & Key Components
- **Root Pages**: Main site entry points (`index.php`, `about.php`, `services.php`, `projects.php`, `contact.php`, etc.).
- **Admin Panel**: All content (services, projects, team, blog, site options) is managed via `/admin/` scripts.
- **Templates**: Shared UI in `/templates/` (header, footer, hero-cta, etc.).
- **Components**: Modular sections (e.g., `components/footer/` with loader, CSS, and sections; see its README for structure).
- **Assets**: Static files in `/assets/` (CSS, JS, images). Image requirements are documented in [`/assets/images/README.md`](assets/images/README.md).
- **Includes**: Helper scripts in `/includes/` (functions, advanced slider helpers, hero detection).
- **Config**: Central config in [`config.php`](config.php) (site settings, DB, security, theme options).

## Developer Workflows
- **Setup**:
  1. Upload files to server
  2. Create MySQL database
  3. Visit [`install.php`](install.php) to initialize DB
  4. Delete `install.php` after setup
  5. Access admin at `/admin/`
- **Theme Customization**: Update via admin or directly in [`config.php`](config.php) and `/templates/`.
- **Database**: Structure in [`test_delete/database.sql`](test_delete/database.sql). All content managed via MySQL.
- **File Uploads**: Images go to `/assets/images/uploads/` (auto-created if missing).
- **Footer**: Use `<?php loadFooter(); ?>` for modular footer loading. Edit sections in `components/footer/sections/`.
- **Error Reporting**: Controlled by environment detection in [`config.php`](config.php) (production disables display, logs to `error.log`).

## Project-Specific Conventions
- **Clean URLs**: SEO-friendly, routed via [`.htaccess`](.htaccess) (no `.php` extensions in public URLs).
- **Theme Options**: Defaults in [`config.php`](config.php), overridden by DB via admin.
- **Security**: CSRF tokens, session hardening, and security headers in [`config.php`](config.php). All DB queries use prepared statements.
- **Responsive Design**: CSS in `/assets/css/` follows an 8-point grid and mobile-first approach.
- **Typography**: Uses Montserrat and Lato via Google Fonts.
- **Integration Points**: SMTP email (configurable in [`config.php`](config.php)), Google Maps (contact page), Google Fonts.

## Examples
- To add a new service: Use `/admin/services.php` or update DB directly.
- To change site logo: Update via admin or replace file in `/assets/images/`.
- To customize footer: Edit [`/templates/footer.php`](templates/footer.php) and theme options in admin.
- To update hero slider: Use `/admin/sliders.php` and reference [`/includes/advanced-slider-helpers.php`](includes/advanced-slider-helpers.php) for logic.

## References
- For setup: [`INSTALLATION.txt`](INSTALLATION.txt), [`test_delete/README.md`](test_delete/README.md), [`test_delete/documentation.html`](test_delete/documentation.html)
- For DB schema: [`test_delete/database.sql`](test_delete/database.sql)
- For changelog: [`test_delete/Changelog.txt`](test_delete/Changelog.txt)
- For image requirements: [`/assets/images/README.md`](assets/images/README.md)
- For footer architecture: [`components/footer/README.md`](components/footer/README.md)

---
**Agents must follow existing patterns for security, modularity, and maintainability. When in doubt, reference the admin panel, config files, and component READMEs for conventions.**
