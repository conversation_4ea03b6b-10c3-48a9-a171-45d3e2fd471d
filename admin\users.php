<?php
/**
 * User Management - Admin Panel
 * Super Admin interface for managing admin users
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/admin-auth.php';

// Require Super Admin access
requireSuperAdmin();

$db = Database::getConnection();
$message = '';
$error = '';

// Handle actions
if ($_POST) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        throw new Exception('Invalid security token. Please try again.');
    }

    try {
        switch ($_POST['action']) {
            case 'create_user':
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $password = $_POST['password'];
                $role = $_POST['role'];
                
                // Validate inputs
                if (empty($username) || empty($email) || empty($password)) {
                    throw new Exception('All fields are required');
                }
                
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('Invalid email address');
                }
                
                if (strlen($password) < 8) {
                    throw new Exception('Password must be at least 8 characters');
                }

                if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/', $password)) {
                    throw new Exception('Password must contain at least 8 characters with uppercase, lowercase, number, and special character (@$!%*?&)');
                }
                
                // Check if username or email already exists
                $stmt = $db->prepare("SELECT id FROM admin_users WHERE username = ? OR email = ?");
                $stmt->execute([$username, $email]);
                if ($stmt->fetch()) {
                    throw new Exception('Username or email already exists');
                }
                
                // Create user
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $db->prepare("INSERT INTO admin_users (username, password, email, role, created_by) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$username, $hashed_password, $email, $role, $_SESSION['admin_user_id']]);
                
                $message = 'User created successfully';
                break;
                
            case 'update_user':
                $user_id = $_POST['user_id'];
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $role = $_POST['role'];
                $status = $_POST['status'];
                
                // Validate inputs
                if (empty($username) || empty($email)) {
                    throw new Exception('Username and email are required');
                }
                
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('Invalid email address');
                }
                
                // Check if username or email already exists for other users
                $stmt = $db->prepare("SELECT id FROM admin_users WHERE (username = ? OR email = ?) AND id != ?");
                $stmt->execute([$username, $email, $user_id]);
                if ($stmt->fetch()) {
                    throw new Exception('Username or email already exists');
                }
                
                // Update user
                $stmt = $db->prepare("UPDATE admin_users SET username = ?, email = ?, role = ?, status = ? WHERE id = ?");
                $stmt->execute([$username, $email, $role, $status, $user_id]);
                
                $message = 'User updated successfully';
                break;
                
            case 'reset_password':
                $user_id = $_POST['user_id'];
                $new_password = $_POST['new_password'];
                
                if (strlen($new_password) < 8) {
                    throw new Exception('Password must be at least 8 characters');
                }

                if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/', $new_password)) {
                    throw new Exception('Password must contain at least 8 characters with uppercase, lowercase, number, and special character (@$!%*?&)');
                }
                
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE admin_users SET password = ? WHERE id = ?");
                $stmt->execute([$hashed_password, $user_id]);
                
                $message = 'Password reset successfully';
                break;
                
            case 'delete_user':
                $user_id = $_POST['user_id'];
                
                // Prevent deleting self
                if ($user_id == $_SESSION['admin_user_id']) {
                    throw new Exception('Cannot delete your own account');
                }
                
                // Delete user and related sessions
                $stmt = $db->prepare("DELETE FROM admin_users WHERE id = ?");
                $stmt->execute([$user_id]);
                
                $message = 'User deleted successfully';
                break;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get all users
$stmt = $db->query("SELECT id, username, email, role, status, created_date, last_login FROM admin_users ORDER BY created_date DESC");
$users = $stmt->fetchAll();

// Render the page using the admin theme system
renderAdminPage('users', [
    'page_title' => 'User Management',
    'page_icon' => 'fas fa-users-cog',
    'page_description' => 'Manage admin users, roles, and permissions.',
    'show_page_header' => true,
    'content_file' => __DIR__ . '/theme/content/users-table.php',
    'message' => $message,
    'error' => $error,
    'users' => $users
]);
?>
