<?php
/**
 * Admin Navigation Template
 * Navigation component for admin dashboard
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Include authentication functions if not already loaded
if (!function_exists('getCurrentAdminUser')) {
    // Try multiple possible paths
    $possible_paths = [
        dirname(dirname(dirname(__FILE__))) . '/includes/admin-auth.php',
        __DIR__ . '/../../../includes/admin-auth.php',
        $_SERVER['DOCUMENT_ROOT'] . '/monolith-design/includes/admin-auth.php'
    ];

    foreach ($possible_paths as $auth_file) {
        if (file_exists($auth_file)) {
            require_once $auth_file;
            break;
        }
    }
}

// Get current page for active state
$current_page = basename($_SERVER['PHP_SELF']);
$current_user = function_exists('getCurrentAdminUser') ? getCurrentAdminUser() : null;

// Define navigation items based on user role
$nav_items = [];

// Super Admin gets full navigation
if (function_exists('isSuperAdmin') && isSuperAdmin()) {
    $nav_items = [
    [
        'title' => 'Theme',
        'url' => 'index.php',
        'icon' => 'fas fa-palette',
        'active' => $current_page === 'index.php'
    ],
    [
        'title' => 'Sliders',
        'url' => 'sliders.php',
        'icon' => 'fas fa-images',
        'active' => $current_page === 'sliders.php'
    ],
    [
        'title' => 'Hero',
        'url' => 'hero-sections.php',
        'icon' => 'fas fa-star',
        'active' => $current_page === 'hero-sections.php'
    ],
    [
        'title' => 'Hero Headers',
        'url' => 'hero-headers.php',
        'icon' => 'fas fa-image',
        'active' => $current_page === 'hero-headers.php'
    ],
    [
        'title' => 'Services',
        'url' => 'services.php',
        'icon' => 'fas fa-tools',
        'active' => $current_page === 'services.php'
    ],
    [
        'title' => 'Projects',
        'url' => 'projects.php',
        'icon' => 'fas fa-building',
        'active' => $current_page === 'projects.php'
    ],
    [
        'title' => 'Team',
        'url' => 'team.php',
        'icon' => 'fas fa-users',
        'active' => $current_page === 'team.php'
    ],
    [
        'title' => 'Reviews',
        'url' => 'testimonials.php',
        'icon' => 'fas fa-quote-right',
        'active' => $current_page === 'testimonials.php'
    ],
    [
        'title' => 'Blog',
        'url' => 'blog.php',
        'icon' => 'fas fa-blog',
        'active' => $current_page === 'blog.php'
    ],
    [
        'title' => 'Contact',
        'url' => 'contacts.php',
        'icon' => 'fas fa-envelope',
        'active' => $current_page === 'contacts.php'
    ],
        [
            'title' => 'Email',
            'url' => 'email-settings.php',
            'icon' => 'fas fa-cog',
            'active' => $current_page === 'email-settings.php'
        ]
    ];
} elseif (function_exists('isContactAdmin') && isContactAdmin()) {
    // Contact Admin gets limited navigation
    $nav_items = [
        [
            'title' => 'Contact',
            'url' => 'contacts.php',
            'icon' => 'fas fa-envelope',
            'active' => $current_page === 'contacts.php'
        ]
    ];
} else {
    // Fallback navigation if auth functions not available
    $nav_items = [
        [
            'title' => 'Theme',
            'url' => 'index.php',
            'icon' => 'fas fa-palette',
            'active' => $current_page === 'index.php'
        ],
        [
            'title' => 'Contact',
            'url' => 'contacts.php',
            'icon' => 'fas fa-envelope',
            'active' => $current_page === 'contacts.php'
        ]
    ];
}
?>

<nav class="admin-nav">
    <div class="admin-nav-container">

        <!-- Navigation Items -->
        <div class="admin-nav-content">
            <ul class="admin-nav-list">
                <?php foreach ($nav_items as $item): ?>
                    <li class="nav-item">
                        <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                           class="nav-link <?php echo $item['active'] ? 'active' : ''; ?>"
                           <?php if ($item['active']): ?>aria-current="page"<?php endif; ?>>
                            <i class="<?php echo htmlspecialchars($item['icon']); ?> nav-icon"></i>
                            <span class="nav-text"><?php echo htmlspecialchars($item['title']); ?></span>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <!-- Navigation Actions -->
        <div class="nav-actions d-none d-lg-flex">
            <div class="nav-status">
                <span class="status-indicator online" title="System Online"></span>
                <span class="status-text">Online</span>
            </div>
        </div>
        
    </div>
</nav>
