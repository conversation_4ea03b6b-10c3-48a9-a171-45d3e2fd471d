<?php
/**
 * Test Newsletter AJAX Functionality
 * This script tests the newsletter signup by simulating AJAX requests
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>🔧 NEWSLETTER AJAX DEBUG TEST</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .error{color:red;} .success{color:green;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:4px;}</style>";

// Test 1: Check database tables
echo "<h2>📋 Step 1: Database Check</h2>";
try {
    $db = Database::getConnection();
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // Check newsletter_subscribers table
    $stmt = $db->prepare("SHOW TABLES LIKE 'newsletter_subscribers'");
    $stmt->execute();
    if ($stmt->fetch()) {
        echo "<div class='success'>✅ newsletter_subscribers table exists</div>";
    } else {
        echo "<div class='error'>❌ newsletter_subscribers table missing</div>";
        
        // Create the table
        echo "<div class='info'>🔧 Creating newsletter_subscribers table...</div>";
        $create_sql = "
            CREATE TABLE newsletter_subscribers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) NOT NULL UNIQUE,
                source VARCHAR(100) DEFAULT 'unknown',
                page VARCHAR(100) DEFAULT 'unknown',
                subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('active', 'inactive', 'unsubscribed') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_status (status),
                INDEX idx_source (source)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $db->exec($create_sql);
        echo "<div class='success'>✅ newsletter_subscribers table created</div>";
    }
    
    // Check contact_submissions table
    $stmt = $db->prepare("SHOW TABLES LIKE 'contact_submissions'");
    $stmt->execute();
    if ($stmt->fetch()) {
        echo "<div class='success'>✅ contact_submissions table exists</div>";
        
        // Check if is_newsletter_signup column exists
        $stmt = $db->prepare("SHOW COLUMNS FROM contact_submissions LIKE 'is_newsletter_signup'");
        $stmt->execute();
        if (!$stmt->fetch()) {
            echo "<div class='info'>🔧 Adding is_newsletter_signup column...</div>";
            $db->exec("ALTER TABLE contact_submissions ADD COLUMN is_newsletter_signup BOOLEAN DEFAULT FALSE");
            echo "<div class='success'>✅ is_newsletter_signup column added</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}

// Test 2: Simulate AJAX POST request
echo "<h2>📋 Step 2: Simulate AJAX Request</h2>";

// Save original values
$original_method = $_SERVER['REQUEST_METHOD'] ?? '';
$original_post = $_POST;
$original_ajax = $_SERVER['HTTP_X_REQUESTED_WITH'] ?? '';

try {
    // Set up AJAX POST simulation
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
    $_POST = [
        'email' => 'ajax-test-' . time() . '@example.com',
        'source' => 'ajax_test',
        'page' => 'test_page'
    ];
    
    echo "<div class='info'>🔧 Simulating AJAX POST request:</div>";
    echo "<pre>";
    echo "Method: " . $_SERVER['REQUEST_METHOD'] . "\n";
    echo "AJAX Header: " . $_SERVER['HTTP_X_REQUESTED_WITH'] . "\n";
    echo "POST Data: " . print_r($_POST, true);
    echo "</pre>";
    
    // Capture output from newsletter-signup.php
    ob_start();
    
    // Include the newsletter signup handler
    include __DIR__ . '/../newsletter-signup.php';
    
    $output = ob_get_clean();
    
    echo "<div class='info'>📤 Raw output from newsletter-signup.php:</div>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    // Try to parse as JSON
    $json_data = json_decode($output, true);
    if ($json_data) {
        echo "<div class='success'>✅ Valid JSON response received</div>";
        echo "<div class='info'>Success: " . ($json_data['success'] ? 'true' : 'false') . "</div>";
        echo "<div class='info'>Message: " . htmlspecialchars($json_data['message']) . "</div>";
        
        if ($json_data['success']) {
            echo "<div class='success'>🎉 Newsletter signup successful!</div>";
        } else {
            echo "<div class='error'>❌ Newsletter signup failed: " . htmlspecialchars($json_data['message']) . "</div>";
        }
    } else {
        echo "<div class='error'>❌ Invalid JSON response</div>";
        echo "<div class='error'>JSON Error: " . json_last_error_msg() . "</div>";
        
        // Check if it's a redirect
        if (strpos($output, 'Location:') !== false) {
            echo "<div class='info'>ℹ️ Response appears to be a redirect (not AJAX)</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Exception during AJAX test: " . $e->getMessage() . "</div>";
    echo "<div class='error'>File: " . $e->getFile() . "</div>";
    echo "<div class='error'>Line: " . $e->getLine() . "</div>";
}

// Restore original values
$_SERVER['REQUEST_METHOD'] = $original_method;
$_POST = $original_post;
if ($original_ajax) {
    $_SERVER['HTTP_X_REQUESTED_WITH'] = $original_ajax;
} else {
    unset($_SERVER['HTTP_X_REQUESTED_WITH']);
}

// Test 3: Check recent database entries
echo "<h2>📋 Step 3: Database Verification</h2>";
try {
    $db = Database::getConnection();
    
    // Check recent newsletter subscribers
    $stmt = $db->prepare("SELECT * FROM newsletter_subscribers ORDER BY subscribed_at DESC LIMIT 3");
    $stmt->execute();
    $recent = $stmt->fetchAll();
    
    echo "<div class='info'>📊 Recent newsletter subscribers:</div>";
    if ($recent) {
        echo "<pre>";
        foreach ($recent as $sub) {
            echo "📧 {$sub['email']} (from {$sub['source']}) - {$sub['subscribed_at']}\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='info'>ℹ️ No newsletter subscribers found</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database verification error: " . $e->getMessage() . "</div>";
}

// Test 4: Test the actual AJAX endpoint
echo "<h2>📋 Step 4: Live AJAX Test Form</h2>";
?>

<div style="background:#f9f9f9;padding:20px;border-radius:8px;margin:20px 0;">
    <h3>🧪 Live AJAX Test</h3>
    <p>This form will test the actual AJAX functionality:</p>
    
    <form id="liveTestForm" style="margin-bottom:20px;">
        <div style="margin-bottom:10px;">
            <label>Email:</label>
            <input type="email" id="testEmail" value="live-test-<?php echo time(); ?>@example.com" style="width:300px;padding:8px;">
        </div>
        <div style="margin-bottom:10px;">
            <label>Source:</label>
            <select id="testSource" style="padding:8px;">
                <option value="footer">Footer</option>
                <option value="hero_cta">Hero CTA</option>
                <option value="live_test">Live Test</option>
            </select>
        </div>
        <button type="submit" style="background:#007cba;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;">
            Test Newsletter Signup
        </button>
    </form>
    
    <div id="testResult" style="display:none;padding:10px;border-radius:4px;margin-top:10px;"></div>
</div>

<script>
document.getElementById('liveTestForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('testEmail').value;
    const source = document.getElementById('testSource').value;
    const resultDiv = document.getElementById('testResult');
    
    const formData = new FormData();
    formData.append('email', email);
    formData.append('source', source);
    formData.append('page', 'ajax_test');
    
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '<div style="color:blue;">🔄 Testing AJAX request...</div>';
    
    console.log('Sending AJAX request to:', '/monolith-design/newsletter-signup.php');
    console.log('Form data:', Object.fromEntries(formData));
    
    fetch('/monolith-design/newsletter-signup.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
        
        try {
            const data = JSON.parse(text);
            if (data.success) {
                resultDiv.innerHTML = '<div style="color:green;">✅ SUCCESS: ' + data.message + '</div>';
            } else {
                resultDiv.innerHTML = '<div style="color:red;">❌ FAILED: ' + data.message + '</div>';
            }
        } catch (e) {
            resultDiv.innerHTML = '<div style="color:red;">❌ INVALID JSON: ' + text + '</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.innerHTML = '<div style="color:red;">❌ AJAX ERROR: ' + error.message + '</div>';
    });
});
</script>

<?php
echo "<h2>🎯 Summary</h2>";
echo "<div class='info'>This test script checks:</div>";
echo "<ul>";
echo "<li>✅ Database connection and table structure</li>";
echo "<li>✅ AJAX request simulation</li>";
echo "<li>✅ JSON response validation</li>";
echo "<li>✅ Live AJAX testing form</li>";
echo "</ul>";
echo "<div class='info'>Use the live test form above to see exactly what happens with AJAX requests.</div>";
?>
