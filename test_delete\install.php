<?php
/**
 * Monolith Design Co. - Database Installer
 * Run this file once to set up the database and tables
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Check if database already exists and has tables
try {
    $db = Database::getConnection();
    
    // Check if tables exist
    $stmt = $db->query("SHOW TABLES LIKE 'sliders'");
    if ($stmt->rowCount() > 0) {
        echo "<h2>Database Already Installed</h2>";
        echo "<p>The database and tables already exist. If you want to reinstall, please drop the database first.</p>";
        echo "<p><a href='index.php'>Go to Homepage</a></p>";
        exit;
    }
} catch (Exception $e) {
    // Database doesn't exist or connection failed
}

// Read and execute SQL file
$sqlFile = __DIR__ . '/database.sql';
if (!file_exists($sqlFile)) {
    die("SQL file not found: $sqlFile");
}

$sql = file_get_contents($sqlFile);
if ($sql === false) {
    die("Could not read SQL file");
}

// Split SQL into individual statements
$statements = array_filter(
    array_map('trim', explode(';', $sql)),
    'strlen'
);

$success = true;
$errors = [];

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                $errors[] = "Error executing statement: " . $e->getMessage();
                $success = false;
            }
        }
    }
    
} catch (PDOException $e) {
    $errors[] = "Database connection error: " . $e->getMessage();
    $success = false;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Installation - Monolith Design Co.</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .success {
            color: #E67E22;
            background-color: #fdf6f0;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .error {
            color: #e74c3c;
            background-color: #fdf2f2;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #E67E22;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #d35400;
        }
        .btn-secondary {
            background-color: #95a5a6;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        h1 {
            color: #1A1A1A;
            margin-bottom: 30px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #E67E22;
            font-size: 2.5rem;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>MONOLITH DESIGN CO.</h1>
        </div>
        
        <h1>Database Installation</h1>
        
        <?php if ($success): ?>
            <div class="success">
                <h2>✅ Installation Successful!</h2>
                <p>The database has been created and populated with sample data successfully.</p>
                <p>You can now use the website with the following features:</p>
                <ul>
                    <li>3 Hero slider slides</li>
                    <li>4 Service pages</li>
                    <li>4 Sample projects</li>
                    <li>3 Client testimonials</li>
                    <li>4 Team members</li>
                    <li>Theme customization options</li>
                </ul>
                <p><strong>Next Steps:</strong></p>
                <ol>
                    <li>Delete this install.php file for security</li>
                    <li>Add your own content through the admin panel</li>
                    <li>Upload your own images to replace the placeholders</li>
                    <li>Customize the theme options</li>
                </ol>
            </div>
            
            <a href="index.php" class="btn">View Website Homepage</a>
            <a href="admin/" class="btn btn-secondary">Access Admin Panel</a>
            
        <?php else: ?>
            <div class="error">
                <h2>❌ Installation Failed</h2>
                <p>There were errors during the installation process:</p>
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                
                <h3>Common Solutions:</h3>
                <ul>
                    <li>Check your database credentials in config.php</li>
                    <li>Make sure MySQL server is running</li>
                    <li>Ensure the database user has CREATE privileges</li>
                    <li>Verify the database name doesn't already exist</li>
                </ul>
            </div>
            
            <a href="install.php" class="btn">Try Again</a>
        <?php endif; ?>
        
        <hr style="margin: 40px 0; border: none; border-top: 1px solid #eee;">
        
        <h3>Database Configuration</h3>
        <p><strong>Host:</strong> <?php echo DB_HOST; ?></p>
        <p><strong>Database:</strong> <?php echo DB_NAME; ?></p>
        <p><strong>User:</strong> <?php echo DB_USER; ?></p>
        <p><strong>Character Set:</strong> <?php echo DB_CHARSET; ?></p>
        
        <h3>Installation Notes</h3>
        <ul>
            <li>Default admin login will be available at <code>/admin/</code></li>
            <li>Sample content includes architectural and engineering projects</li>
            <li>All images are placeholder URLs - replace with your own</li>
            <li>Theme options can be customized through the admin panel</li>
            <li>Clean URLs are enabled via .htaccess</li>
        </ul>
    </div>
</body>
</html>
