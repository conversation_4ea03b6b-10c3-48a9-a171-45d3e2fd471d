const { test, expect } = require('@playwright/test');

test.describe('Hero CTA Newsletter Tests', () => {
  test('should successfully submit newsletter signup via hero CTA', async ({ page }) => {
    // Navigate to a page with hero CTA newsletter form
    await page.goto('http://localhost/monolith-design/contact');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if hero newsletter form exists
    const heroForm = page.locator('#heroNewsletterForm');
    if (await heroForm.count() === 0) {
      console.log('Hero newsletter form not found on contact page, skipping test');
      return;
    }
    
    console.log('Hero newsletter form found');
    
    // Fill in the email field
    const emailInput = heroForm.locator('input[type="email"]');
    await emailInput.fill('<EMAIL>');
    
    // Listen for the network request to see what's actually being sent
    const requestPromise = page.waitForRequest(request => 
      request.url().includes('newsletter-signup.php')
    );
    
    // Listen for the response
    const responsePromise = page.waitForResponse(response => 
      response.url().includes('newsletter-signup.php')
    );
    
    // Submit the form
    await heroForm.locator('button[type="submit"]').click();
    
    // Wait for the request and response
    const request = await requestPromise;
    const response = await responsePromise;
    
    console.log('Request method:', request.method());
    console.log('Request headers:', request.headers());
    console.log('Response status:', response.status());
    
    const responseBody = await response.text();
    console.log('Response body:', responseBody);
    
    // Parse the JSON response
    const jsonResponse = JSON.parse(responseBody);
    
    // Check if the request was successful
    expect(jsonResponse.success).toBe(true);
    expect(jsonResponse.message).toContain('Thank you');
    
    // Check if success message is displayed
    const successMessage = page.locator('#heroNewsletterSuccessMessage');
    await expect(successMessage).toBeVisible({ timeout: 5000 });
    
    // Check if form is hidden
    await expect(heroForm).toBeHidden({ timeout: 5000 });
    
    console.log('✅ Hero CTA newsletter test passed');
  });
  
  test('should handle duplicate newsletter submission correctly', async ({ page }) => {
    await page.goto('http://localhost/monolith-design/contact');
    await page.waitForLoadState('networkidle');
    
    const heroForm = page.locator('#heroNewsletterForm');
    if (await heroForm.count() === 0) {
      console.log('Hero newsletter form not found, skipping duplicate test');
      return;
    }
    
    const testEmail = `duplicate-test-${Date.now()}@example.com`;
    
    // First submission
    await heroForm.locator('input[type="email"]').fill(testEmail);
    await heroForm.locator('button[type="submit"]').click();
    
    // Wait for success message
    await expect(page.locator('#heroNewsletterSuccessMessage')).toBeVisible();
    
    // Wait for form to reappear
    await expect(heroForm).toBeVisible({ timeout: 6000 });
    
    // Second submission with same email
    await heroForm.locator('input[type="email"]').fill(testEmail);
    
    const responsePromise = page.waitForResponse(response => 
      response.url().includes('newsletter-signup.php')
    );
    
    await heroForm.locator('button[type="submit"]').click();
    
    const response = await responsePromise;
    const responseBody = await response.text();
    const jsonResponse = JSON.parse(responseBody);
    
    // Should still show success (we don't reveal duplicates for security)
    expect(jsonResponse.success).toBe(true);
    
    console.log('✅ Duplicate newsletter submission handled correctly');
  });
});

test.describe('Contact Form Tests', () => {
  test('should prevent duplicate contact form submissions', async ({ page }) => {
    await page.goto('http://localhost/monolith-design/contact');
    await page.waitForLoadState('networkidle');
    
    const testEmail = `contact-duplicate-${Date.now()}@example.com`;
    
    // Fill out contact form
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="phone"]', '************');
    await page.selectOption('select[name="service"]', { index: 1 });
    await page.fill('textarea[name="message"]', 'This is a test message');
    
    // Submit first time
    await page.click('button[name="submit_contact"]');
    await page.waitForLoadState('networkidle');
    
    // Check for success message
    const successMessage = page.locator('.contact-form .success, .form-success, [class*="success"]');
    if (await successMessage.count() > 0) {
      console.log('First submission successful');
    }
    
    // Try to submit again with same email
    await page.fill('input[name="name"]', 'Test User Again');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="phone"]', '************');
    await page.selectOption('select[name="service"]', { index: 2 });
    await page.fill('textarea[name="message"]', 'This should be blocked');
    
    await page.click('button[name="submit_contact"]');
    await page.waitForLoadState('networkidle');
    
    // Check for duplicate prevention message
    const errorMessage = page.locator('.contact-form .error, .form-error, [class*="error"]');
    const pageContent = await page.textContent('body');
    
    // Should contain duplicate prevention message
    expect(pageContent).toContain('already submitted');
    
    console.log('✅ Contact form duplicate prevention working');
  });
});
