<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

// Check if we're running the migration
if (isset($_GET['migrate']) && $_GET['migrate'] === 'true') {
    $currentLogoSize = getThemeOption('logo_size', 'medium');
    $conversionMap = ['small' => 75, 'medium' => 100, 'large' => 150];
    $newPercentage = $conversionMap[$currentLogoSize] ?? 100;
    
    updateThemeOption('logo_size_percent', $newPercentage);
    $message = "Migration completed! Converted '{$currentLogoSize}' to {$newPercentage}%";
}

$currentLogoSize = getThemeOption('logo_size', 'medium');
$currentPercent = getThemeOption('logo_size_percent', '100');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Logo Percentage System Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .logo-preview { margin: 20px 0; }
        .logo-preview img { border: 1px solid #ccc; }
        .success { color: green; }
        .info { color: blue; }
        .slider-control { margin: 20px 0; }
        .slider-control input[type="range"] { width: 300px; }
    </style>
</head>
<body>
    <h1>Logo Percentage System Test</h1>
    
    <?php if (isset($message)): ?>
        <div class="success"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>Current Settings</h2>
        <p><strong>Old logo_size:</strong> <?php echo htmlspecialchars($currentLogoSize); ?></p>
        <p><strong>New logo_size_percent:</strong> <?php echo htmlspecialchars($currentPercent); ?>%</p>
        
        <?php if (!getThemeOption('logo_size_percent')): ?>
            <p><a href="?migrate=true" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none;">Run Migration</a></p>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>Logo Preview Test</h2>
        <div class="slider-control">
            <label>Test Logo Size: <span id="size-display">100%</span></label><br>
            <input type="range" id="logo-slider" min="50" max="200" step="5" value="100" 
                   oninput="updateLogoPreview(this.value)">
        </div>
        
        <div class="logo-preview">
            <h3>Header Logo (Black)</h3>
            <img id="header-logo" src="<?php echo getThemeOption('site_logo', 'assets/images/logo.svg'); ?>" 
                 alt="Header Logo" style="--logo-size-percent: <?php echo $currentPercent; ?>; width: calc(140px * (var(--logo-size-percent) / 100)); height: auto;">
        </div>
        
        <div class="logo-preview">
            <h3>Footer Logo (White)</h3>
            <div style="background: #333; padding: 20px;">
                <img id="footer-logo" src="<?php echo getThemeOption('site_logo_white', 'assets/images/logo-white.svg'); ?>" 
                     alt="Footer Logo" style="--logo-size-percent: <?php echo $currentPercent; ?>; width: calc(140px * (var(--logo-size-percent) / 100)); height: auto;">
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>System Status</h2>
        <ul>
            <li class="<?php echo getThemeOption('logo_size_percent') ? 'success' : 'info'; ?>">
                Percentage system: <?php echo getThemeOption('logo_size_percent') ? 'Active' : 'Not migrated'; ?>
            </li>
            <li class="info">Admin interface: Updated with slider control</li>
            <li class="info">Frontend templates: Updated with CSS custom properties</li>
            <li class="info">Responsive breakpoints: Implemented</li>
        </ul>
    </div>
    
    <script>
        function updateLogoPreview(value) {
            document.getElementById('size-display').textContent = value + '%';
            
            const headerLogo = document.getElementById('header-logo');
            const footerLogo = document.getElementById('footer-logo');
            
            headerLogo.style.setProperty('--logo-size-percent', value);
            headerLogo.style.width = `calc(140px * (${value} / 100))`;
            
            footerLogo.style.setProperty('--logo-size-percent', value);
            footerLogo.style.width = `calc(140px * (${value} / 100))`;
        }
        
        // Initialize with current percentage
        updateLogoPreview(<?php echo $currentPercent; ?>);
    </script>
</body>
</html>
