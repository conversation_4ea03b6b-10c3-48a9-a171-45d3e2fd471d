<?php
/**
 * Test Hero CTA with Fixed JavaScript
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

// Set up hero page name for testing
$hero_page_name = 'contact';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero CTA Fixed Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        .test-info { background: #e7f3ff; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #007cba; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }
        
        /* Hero CTA Section Styling */
        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 20px;
            text-align: center;
            border-radius: 12px;
            margin: 30px 0;
        }
        
        .cta-content h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .cta-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Hero CTA Fixed Test</h1>
        
        <div class="test-info">
            <strong>🔧 FIXES APPLIED:</strong><br>
            1. Added null check for successMessage element<br>
            2. Enhanced console logging for debugging<br>
            3. Improved error handling in JavaScript<br>
            4. Testing with actual hero-cta.php template
        </div>
        
        <div class="info">
            <strong>📋 Test Instructions:</strong><br>
            1. Enter a valid email address<br>
            2. Click Subscribe<br>
            3. Watch for success message (should appear for 5 seconds)<br>
            4. Check browser console for detailed logs<br>
            5. Try with different email to test again
        </div>
        
        <?php
        // Include the actual hero-cta template
        echo "<h2>🎯 Hero CTA Section (Using Actual Template)</h2>";
        
        try {
            // Load the hero-cta template
            include __DIR__ . '/../templates/hero-cta.php';
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error loading hero-cta template: " . $e->getMessage() . "</div>";
        }
        ?>
        
        <div class="test-info">
            <strong>🔍 Debug Information:</strong><br>
            - Hero Page Name: <?php echo $hero_page_name; ?><br>
            - Current Page: <?php echo isset($current_page) ? $current_page : 'Not set'; ?><br>
            - Newsletter Enabled: <?php echo isset($show_newsletter) ? ($show_newsletter ? 'YES' : 'NO') : 'Not checked'; ?><br>
            - Success Message: <?php echo isset($newsletter_success_message) ? "'{$newsletter_success_message}'" : 'Not set'; ?>
        </div>
        
        <script>
        // Additional debugging
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== HERO CTA DEBUG INFO ===');
            console.log('Form element:', document.getElementById('heroNewsletterForm'));
            console.log('Success message element:', document.getElementById('heroNewsletterSuccessMessage'));
            
            const form = document.getElementById('heroNewsletterForm');
            const successMessage = document.getElementById('heroNewsletterSuccessMessage');
            
            if (!form) {
                console.error('❌ Hero newsletter form not found!');
                document.body.insertAdjacentHTML('beforeend', '<div class="error">❌ Hero newsletter form not found! Newsletter may not be enabled for this page.</div>');
            } else {
                console.log('✅ Hero newsletter form found');
            }
            
            if (!successMessage) {
                console.error('❌ Success message element not found!');
                document.body.insertAdjacentHTML('beforeend', '<div class="error">❌ Success message element not found!</div>');
            } else {
                console.log('✅ Success message element found');
            }
            
            if (form && successMessage) {
                console.log('✅ Both elements found - hero CTA should work correctly');
                document.body.insertAdjacentHTML('beforeend', '<div class="success">✅ Hero CTA elements found - ready for testing!</div>');
            }
        });
        </script>
    </div>
</body>
</html>
