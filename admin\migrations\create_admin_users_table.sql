-- Migration: Create admin_users table for Monolith Design admin system
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(64) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(128) NOT NULL,
    role ENUM('super_admin', 'contact_admin') NOT NULL DEFAULT 'contact_admin',
    created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME DEFAULT NULL,
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert initial super admin (replace password hash with real one after first login)
INSERT INTO admin_users (username, password, email, role, status)
VALUES ('admin', '$2y$10$REPLACE_WITH_HASHED_PASSWORD', '<EMAIL>', 'super_admin', 'active');
