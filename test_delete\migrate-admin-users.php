<?php
/**
 * Admin Users Migration Script
 * Creates admin_users table and migrates to database-driven authentication
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h2>Admin Users Migration Script</h2>\n";
echo "<p>Creating admin_users table and setting up role-based authentication...</p>\n";

try {
    $db = Database::getConnection();
    
    // Read and execute SQL migration
    $sql = file_get_contents(__DIR__ . '/create-admin-users-table.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $db->exec($statement);
                $successCount++;
                echo "<p style='color: green;'>✓ Executed SQL statement successfully</p>\n";
            } catch (PDOException $e) {
                echo "<p style='color: orange;'>⚠ SQL statement skipped (may already exist): " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
    }
    
    echo "<p style='color: green;'><strong>✓ Migration completed successfully!</strong></p>\n";
    echo "<p>Executed {$successCount} SQL statements.</p>\n";
    
    // Verify table creation
    $stmt = $db->query("SHOW TABLES LIKE 'admin_users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ admin_users table created successfully</p>\n";
        
        // Check if users were inserted
        $stmt = $db->query("SELECT username, email, role FROM admin_users");
        $users = $stmt->fetchAll();
        
        echo "<h3>Created Admin Users:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Username</th><th>Email</th><th>Role</th></tr>\n";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
    } else {
        echo "<p style='color: red;'>✗ admin_users table was not created</p>\n";
    }
    
    // Verify other tables
    $tables = ['admin_sessions', 'admin_login_attempts'];
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ {$table} table created successfully</p>\n";
        } else {
            echo "<p style='color: red;'>✗ {$table} table was not created</p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<h3>Default Login Credentials</h3>\n";
echo "<ul>\n";
echo "<li><strong>Super Admin:</strong> username: <code>admin</code>, password: <code>admin123</code></li>\n";
echo "<li><strong>Contact Admin:</strong> username: <code>contact_admin</code>, password: <code>admin123</code></li>\n";
echo "<li><strong>Support:</strong> username: <code>support</code>, password: <code>admin123</code></li>\n";
echo "</ul>\n";
echo "<p style='color: orange;'><strong>⚠️ Important:</strong> Change these default passwords after first login!</p>\n";

echo "<h3>Next Steps</h3>\n";
echo "<ol>\n";
echo "<li>Update admin/login.php for database authentication</li>\n";
echo "<li>Add role-based access control to admin pages</li>\n";
echo "<li>Update admin navigation for different user roles</li>\n";
echo "<li>Create user management interface</li>\n";
echo "</ol>\n";

echo "<p><a href='../admin/login.php'>← Go to Admin Login</a></p>\n";
?>
