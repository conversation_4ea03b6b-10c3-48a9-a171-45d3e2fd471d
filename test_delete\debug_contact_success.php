<?php
/**
 * Contact Form Success Message Diagnostic Test
 * Simple, comprehensive test to identify the exact issue
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h1>🔍 CONTACT FORM SUCCESS MESSAGE DIAGNOSTIC</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:10px;border-radius:4px;margin:5px 0;} 
.error{color:red;background:#f8d7da;padding:10px;border-radius:4px;margin:5px 0;} 
.info{color:blue;background:#d1ecf1;padding:10px;border-radius:4px;margin:5px 0;} 
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:5px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow-x:auto;}
.form-message{padding:1rem 1.5rem;border-radius:8px;margin:1rem 0;font-weight:500;}
.form-message.success{background-color:#d4edda;color:#155724;border:1px solid #c3e6cb;}
.form-message.error{background-color:#f8d7da;color:#721c24;border:1px solid #f5c6cb;}
</style>";

echo "<div class='info'>🎯 This test will identify exactly why contact form success messages aren't showing</div>";

echo "<h2>📋 TEST 1: Backend Functionality Check</h2>";

// Test with a fresh email to avoid duplicate blocking
$test_email = 'contact-test-' . time() . '@example.com';
$test_data = [
    'name' => 'Test User ' . date('H:i:s'),
    'email' => $test_email,
    'phone' => '555-0123',
    'service' => 'Testing Service',
    'message' => 'This is a diagnostic test message for the contact form success alert system.'
];

echo "<div class='info'>🔄 Testing handleContactForm with fresh email: $test_email</div>";

$result = handleContactForm($test_data);

echo "<div class='info'>📊 Backend Result:</div>";
echo "<pre>";
echo "Success: " . ($result['success'] ? 'TRUE' : 'FALSE') . "\n";
echo "Message: '" . $result['message'] . "'\n";
echo "</pre>";

if ($result['success']) {
    echo "<div class='success'>✅ Backend functionality is working correctly</div>";
    
    // Simulate what contact.php does
    $form_message = $result['message'];
    $form_status = 'success';
    
    echo "<h2>🎨 TEST 2: Message Display Simulation</h2>";
    echo "<div class='info'>📝 Simulating contact.php variables:</div>";
    echo "<pre>";
    echo "\$form_message = '$form_message'\n";
    echo "\$form_status = '$form_status'\n";
    echo "</pre>";
    
    echo "<div class='info'>🎨 This is how the success message should appear:</div>";
    ?>
    
    <!-- Success Message Preview -->
    <div class="form-message success" id="contactFormMessage" style="display: block !important; visibility: visible !important;">
        <strong>✅ Success!</strong>
        <?php echo htmlspecialchars($form_message); ?>
    </div>
    
    <?php
    echo "<div class='success'>✅ If you can see the green success message above, the styling is working</div>";
    
} else {
    echo "<div class='error'>❌ Backend issue detected: {$result['message']}</div>";
    
    // Check if it's a duplicate submission issue
    if (strpos($result['message'], 'already submitted') !== false || strpos($result['message'], 'recently') !== false) {
        echo "<div class='warning'>⚠️ This appears to be a duplicate submission block</div>";
        echo "<div class='info'>💡 The duplicate prevention is working, but it may be too restrictive (10 minutes)</div>";
        
        // Check recent submissions
        try {
            $db = Database::getConnection();
            $stmt = $db->prepare("
                SELECT email, created_at 
                FROM contact_submissions 
                WHERE is_newsletter_signup = 0 
                ORDER BY created_at DESC 
                LIMIT 5
            ");
            $stmt->execute();
            $recent = $stmt->fetchAll();
            
            echo "<div class='info'>📋 Recent contact submissions (last 5):</div>";
            echo "<pre>";
            foreach ($recent as $sub) {
                echo "Email: {$sub['email']} - Time: {$sub['created_at']}\n";
            }
            echo "</pre>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Database query error: {$e->getMessage()}</div>";
        }
    }
}

echo "<h2>🔍 TEST 3: Contact.php Integration Check</h2>";

// Check if contact.php properly handles the form processing
$contact_path = __DIR__ . '/contact.php';
if (file_exists($contact_path)) {
    echo "<div class='success'>✅ contact.php exists</div>";
    
    $contact_content = file_get_contents($contact_path);
    
    // Check for critical elements
    $checks = [
        'POST check' => strpos($contact_content, 'if ($_POST && isset($_POST[\'submit_contact\']))') !== false,
        'handleContactForm call' => strpos($contact_content, 'handleContactForm($_POST)') !== false,
        'form_message assignment' => strpos($contact_content, '$form_message = $result[\'message\']') !== false,
        'form_status assignment' => strpos($contact_content, '$form_status = $result[\'success\'] ? \'success\' : \'error\'') !== false,
        'message display block' => strpos($contact_content, 'if ($form_message)') !== false,
        'submit button' => strpos($contact_content, 'name="submit_contact"') !== false
    ];
    
    echo "<div class='info'>🔍 Contact.php structure check:</div>";
    foreach ($checks as $check => $found) {
        $status = $found ? '✅' : '❌';
        $color = $found ? 'success' : 'error';
        echo "<div class='$color'>$status $check</div>";
    }
    
    // Check for popup system
    if (strpos($contact_content, 'contact-popup') !== false) {
        echo "<div class='warning'>⚠️ Contact popup system detected - check if popup JavaScript is working</div>";
    }
    
} else {
    echo "<div class='error'>❌ contact.php not found</div>";
}

echo "<h2>🎯 TEST 4: CSS Styling Check</h2>";

// Check if the CSS for form messages exists
$css_files = [
    'assets/css/arkify-style.css',
    'assets/css/style.css',
    'assets/css/main.css'
];

$css_found = false;
foreach ($css_files as $css_file) {
    $css_path = __DIR__ . '/' . $css_file;
    if (file_exists($css_path)) {
        echo "<div class='success'>✅ Found CSS file: $css_file</div>";
        $css_content = file_get_contents($css_path);
        
        if (strpos($css_content, '.form-message') !== false) {
            echo "<div class='success'>✅ .form-message styles found in $css_file</div>";
            $css_found = true;
        }
    }
}

if (!$css_found) {
    echo "<div class='warning'>⚠️ .form-message CSS styles not found in common CSS files</div>";
    echo "<div class='info'>💡 The inline styles in contact.php should handle this, but external CSS might override</div>";
}

echo "<h2>🧪 TEST 5: Live Form Test</h2>";

?>

<div class="info">📝 Use this form to test the contact functionality live:</div>

<form method="POST" action="" style="background:white;padding:20px;border-radius:8px;margin:20px 0;max-width:500px;">
    <h3>Live Contact Form Test</h3>
    
    <div style="margin-bottom:15px;">
        <label for="test_name" style="display:block;margin-bottom:5px;font-weight:bold;">Name:</label>
        <input type="text" id="test_name" name="name" required value="Live Test User" style="width:100%;padding:8px;border:1px solid #ccc;border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label for="test_email" style="display:block;margin-bottom:5px;font-weight:bold;">Email:</label>
        <input type="email" id="test_email" name="email" required value="live-test-<?php echo time(); ?>@example.com" style="width:100%;padding:8px;border:1px solid #ccc;border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label for="test_phone" style="display:block;margin-bottom:5px;font-weight:bold;">Phone:</label>
        <input type="tel" id="test_phone" name="phone" value="555-0123" style="width:100%;padding:8px;border:1px solid #ccc;border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label for="test_service" style="display:block;margin-bottom:5px;font-weight:bold;">Service:</label>
        <select id="test_service" name="service" style="width:100%;padding:8px;border:1px solid #ccc;border-radius:4px;">
            <option value="Testing">Testing Service</option>
        </select>
    </div>
    
    <div style="margin-bottom:15px;">
        <label for="test_message" style="display:block;margin-bottom:5px;font-weight:bold;">Message:</label>
        <textarea id="test_message" name="message" required rows="4" style="width:100%;padding:8px;border:1px solid #ccc;border-radius:4px;">This is a live test of the contact form success message functionality.</textarea>
    </div>
    
    <button type="submit" name="submit_contact" style="background:#2D5A27;color:white;padding:12px 24px;border:none;border-radius:4px;cursor:pointer;font-weight:bold;">
        Test Contact Form
    </button>
</form>

<?php

// Handle the live test form submission
if ($_POST && isset($_POST['submit_contact'])) {
    echo "<h2>🧪 LIVE TEST RESULTS</h2>";
    
    echo "<div class='info'>📝 POST data received:</div>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    $live_result = handleContactForm($_POST);
    $live_form_message = $live_result['message'];
    $live_form_status = $live_result['success'] ? 'success' : 'error';
    
    echo "<div class='info'>📊 Live test result:</div>";
    echo "<pre>";
    echo "Success: " . ($live_result['success'] ? 'TRUE' : 'FALSE') . "\n";
    echo "Message: '$live_form_message'\n";
    echo "Status: '$live_form_status'\n";
    echo "</pre>";
    
    if ($live_result['success']) {
        echo "<div class='success'>🎉 SUCCESS! The contact form is working correctly!</div>";
        
        // Show the actual success message as it would appear
        ?>
        <div class="form-message success" style="display: block !important; visibility: visible !important;">
            <strong>✅ Success!</strong>
            <?php echo htmlspecialchars($live_form_message); ?>
        </div>
        <?php
        
        echo "<div class='info'>💡 If you can see the green success message above, the system is working</div>";
        echo "<div class='info'>🔍 If this works here but not on /contact, there may be a page-specific issue</div>";
        
    } else {
        echo "<div class='error'>❌ Test failed: $live_form_message</div>";
        
        if (strpos($live_form_message, 'already submitted') !== false) {
            echo "<div class='warning'>⚠️ Duplicate submission detected - try with a different email</div>";
        }
    }
}

echo "<h2>🎯 DIAGNOSIS SUMMARY</h2>";

echo "<div class='info'>
<strong>Common Issues & Solutions:</strong><br><br>

<strong>1. Duplicate Prevention Too Restrictive:</strong><br>
- 10-minute window blocks repeat testing<br>
- Solution: Use different email addresses for testing<br>
- Consider reducing duplicate check time for better UX<br><br>

<strong>2. CSS Styling Issues:</strong><br>
- Check if .form-message.success styles are properly loaded<br>
- Verify no CSS conflicts hiding the message<br>
- The inline styles should override most conflicts<br><br>

<strong>3. JavaScript Interference:</strong><br>
- Check browser console for JavaScript errors<br>
- Verify no scripts are preventing form submission<br>
- Test with JavaScript disabled<br><br>

<strong>4. Popup System Conflicts:</strong><br>
- Contact page may use popup instead of inline message<br>
- Check if popup JavaScript is working correctly<br>
- Verify popup overlay and message display<br>
</div>";

echo "<div class='warning'>💡 Next Steps:</div>";
echo "<div class='info'>1. Test the live form above with a fresh email</div>";
echo "<div class='info'>2. If it works here, compare with actual /contact page</div>";
echo "<div class='info'>3. Check browser developer tools for console errors</div>";
echo "<div class='info'>4. Verify the contact page doesn't have conflicting JavaScript</div>";

?>
