<?php
/**
 * Check Hero Headers Database Records
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h1>Hero Headers Database Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 2rem; }
    .success { color: #27ae60; }
    .error { color: #e74c3c; }
    .info { color: #3498db; }
    .warning { color: #f39c12; }
    table { border-collapse: collapse; width: 100%; margin: 1rem 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

try {
    $db = Database::getConnection();
    
    // Check hero_headers table
    echo "<h2>🔍 Hero Headers Table Records</h2>";
    $stmt = $db->query("SELECT page_name, page_title, active FROM hero_headers ORDER BY page_name");
    $headers = $stmt->fetchAll();
    
    if ($headers) {
        echo "<table>";
        echo "<tr><th>Page Name</th><th>Page Title</th><th>Active</th></tr>";
        foreach ($headers as $header) {
            $status = $header['active'] ? '✅ Yes' : '❌ No';
            echo "<tr>";
            echo "<td>{$header['page_name']}</td>";
            echo "<td>{$header['page_title']}</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ No hero headers found in database</p>";
    }
    
    // Test specific pages
    echo "<h2>🧪 Testing getHeroHeader Function</h2>";
    $test_pages = ['team', 'service-details'];
    
    foreach ($test_pages as $page) {
        echo "<h3>Testing: $page</h3>";
        $hero = getHeroHeader($page);
        
        if ($hero) {
            echo "<p class='success'>✅ Found hero header for '$page'</p>";
            echo "<ul>";
            echo "<li><strong>Title:</strong> " . htmlspecialchars($hero['page_title']) . "</li>";
            echo "<li><strong>Subtitle:</strong> " . htmlspecialchars($hero['subtitle'] ?? 'N/A') . "</li>";
            echo "<li><strong>Active:</strong> " . ($hero['active'] ? 'Yes' : 'No') . "</li>";
            echo "<li><strong>Background Type:</strong> " . ($hero['background_type'] ?? 'N/A') . "</li>";
            echo "</ul>";
        } else {
            echo "<p class='error'>❌ No hero header found for '$page'</p>";
            
            // Check if inactive record exists
            $stmt = $db->prepare("SELECT * FROM hero_headers WHERE page_name = ?");
            $stmt->execute([$page]);
            $inactive = $stmt->fetch();
            
            if ($inactive) {
                echo "<p class='warning'>⚠️ Found inactive record (active = {$inactive['active']})</p>";
            } else {
                echo "<p class='info'>ℹ️ No record exists in database for '$page'</p>";
            }
        }
    }
    
    // Check what templates the pages are using
    echo "<h2>📄 Page Template Analysis</h2>";
    echo "<h3>team.php</h3>";
    $team_content = file_get_contents('team.php');
    if (strpos($team_content, 'templates/page-hero.php') !== false) {
        echo "<p class='warning'>⚠️ team.php uses templates/page-hero.php (OLD system)</p>";
    }
    if (strpos($team_content, 'templates/hero-header.php') !== false) {
        echo "<p class='success'>✅ team.php uses templates/hero-header.php (NEW system)</p>";
    }
    
    echo "<h3>service-details.php</h3>";
    if (file_exists('service-details.php')) {
        $service_content = file_get_contents('service-details.php');
        if (strpos($service_content, 'templates/page-hero.php') !== false) {
            echo "<p class='warning'>⚠️ service-details.php uses templates/page-hero.php (OLD system)</p>";
        }
        if (strpos($service_content, 'templates/hero-header.php') !== false) {
            echo "<p class='success'>✅ service-details.php uses templates/hero-header.php (NEW system)</p>";
        }
    } else {
        echo "<p class='error'>❌ service-details.php not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
