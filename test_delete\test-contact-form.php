<?php
/**
 * Test Contact Form Functionality
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>📝 CONTACT FORM TEST</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:10px;border-radius:4px;margin:5px 0;} 
.error{color:red;background:#f8d7da;padding:10px;border-radius:4px;margin:5px 0;} 
.info{color:blue;background:#d1ecf1;padding:10px;border-radius:4px;margin:5px 0;} 
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:5px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow-x:auto;}
.test-section{background:white;padding:20px;margin:15px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1);}
</style>";

try {
    $db = Database::getConnection();
    echo "<div class='success'>✅ Database connection successful</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🧪 TEST 1: Contact Form Handler</h2>";
    
    // Test with unique email to avoid duplicate check
    $test_email = 'contact-test-' . time() . '@example.com';
    
    $test_data = [
        'name' => 'Test User',
        'email' => $test_email,
        'phone' => '555-0123',
        'service' => 'Testing',
        'message' => 'This is a test message to verify contact form functionality.'
    ];
    
    echo "<div class='info'>🔄 Testing contact form with email: $test_email</div>";
    
    $result = handleContactForm($test_data);
    
    if ($result['success']) {
        echo "<div class='success'>✅ Contact form submission successful!</div>";
        echo "<div class='info'>📝 Success message: '{$result['message']}'</div>";
    } else {
        echo "<div class='error'>❌ Contact form submission failed: {$result['message']}</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🔍 TEST 2: Duplicate Submission Check</h2>";
    
    // Test duplicate submission with same email
    echo "<div class='info'>🔄 Testing duplicate submission with same email...</div>";
    
    $duplicate_result = handleContactForm($test_data);
    
    if (!$duplicate_result['success']) {
        echo "<div class='warning'>⚠️ Duplicate submission blocked (expected behavior)</div>";
        echo "<div class='info'>📝 Block message: '{$duplicate_result['message']}'</div>";
    } else {
        echo "<div class='error'>❌ Duplicate submission was allowed (unexpected!)</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>📊 TEST 3: Recent Contact Submissions</h2>";
    
    $stmt = $db->prepare("
        SELECT name, email, created_at, is_newsletter_signup 
        FROM contact_submissions 
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recent_submissions = $stmt->fetchAll();
    
    if ($recent_submissions) {
        echo "<div class='info'>📋 Recent contact submissions (last hour):</div>";
        echo "<pre>";
        foreach ($recent_submissions as $sub) {
            $type = $sub['is_newsletter_signup'] ? 'NEWSLETTER' : 'CONTACT';
            echo "{$sub['name']} ({$sub['email']}) - $type - {$sub['created_at']}\n";
        }
        echo "</pre>";
    } else {
        echo "<div class='info'>ℹ️ No recent contact submissions found</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🔧 TEST 4: Contact Page Structure</h2>";
    
    // Check contact.php structure
    $contact_path = __DIR__ . '/../contact.php';
    if (file_exists($contact_path)) {
        $contact_content = file_get_contents($contact_path);
        
        $structure_checks = [
            'POST check' => preg_match('/if\s*\(\$_POST\s*&&\s*isset\(\$_POST\[["\']submit_contact["\']\]\)/', $contact_content),
            'handleContactForm call' => preg_match('/handleContactForm\(\$_POST\)/', $contact_content),
            'form_message assignment' => preg_match('/\$form_message\s*=\s*\$result\[["\']message["\']\]/', $contact_content),
            'form_status assignment' => preg_match('/\$form_status\s*=\s*\$result\[["\']success["\']\]/', $contact_content),
            'form_message display' => preg_match('/if\s*\(\$form_message\)/', $contact_content),
            'submit button name' => preg_match('/name=["\']submit_contact["\']/', $contact_content)
        ];
        
        echo "<div class='info'>🔍 Contact.php Structure Check:</div>";
        foreach ($structure_checks as $check => $found) {
            $status = $found ? '✅' : '❌';
            $color = $found ? 'success' : 'error';
            echo "<div class='$color'>$status $check</div>";
        }
        
        // Check for form message CSS classes
        if (preg_match('/\.form-message/', $contact_content)) {
            echo "<div class='success'>✅ form-message CSS class found</div>";
        } else {
            echo "<div class='warning'>⚠️ form-message CSS class not found in contact.php</div>";
        }
        
    } else {
        echo "<div class='error'>❌ contact.php not found</div>";
    }
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🎯 TEST 5: Simulate Contact Form POST</h2>";
    
    // Simulate actual POST request like the contact form would send
    $original_post = $_POST;
    
    $_POST = [
        'submit_contact' => '1',
        'name' => 'Simulated Test User',
        'email' => 'simulated-' . time() . '@example.com',
        'phone' => '555-9999',
        'service' => 'Simulation Test',
        'message' => 'This simulates an actual contact form submission.'
    ];
    
    echo "<div class='info'>🔄 Simulating POST with email: {$_POST['email']}</div>";
    
    // Check if the POST processing would work
    if ($_POST && isset($_POST['submit_contact'])) {
        $result = handleContactForm($_POST);
        $form_message = $result['message'];
        $form_status = $result['success'] ? 'success' : 'error';
        
        echo "<div class='info'>📝 Form message would be: '$form_message'</div>";
        echo "<div class='info'>🎨 Form status would be: '$form_status'</div>";
        
        if ($result['success']) {
            echo "<div class='success'>✅ Simulated contact form would show success message</div>";
        } else {
            echo "<div class='error'>❌ Simulated contact form would show error: {$result['message']}</div>";
        }
    } else {
        echo "<div class='error'>❌ POST simulation failed</div>";
    }
    
    // Restore original POST
    $_POST = $original_post;
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<div class='test-section'>";
echo "<h2>🎯 DIAGNOSIS</h2>";
echo "<div class='info'>
<strong>Contact Form Issues & Solutions:</strong><br><br>

<strong>1. Success Message Not Showing:</strong><br>
- Check if duplicate submission blocking is preventing success<br>
- Verify form has correct submit button name='submit_contact'<br>
- Ensure form_message variable is properly displayed in HTML<br>
- Check CSS styling for .form-message.success class<br><br>

<strong>2. Duplicate Submission Blocking:</strong><br>
- 24-hour duplicate check may be too restrictive<br>
- Consider reducing to 1-hour or adding user feedback<br>
- Test with different email addresses<br><br>

<strong>3. Common Solutions:</strong><br>
- Clear browser cache and test with fresh email<br>
- Check browser developer tools for JavaScript errors<br>
- Verify database is saving submissions correctly<br>
- Test form submission flow step by step<br>
</div>";
echo "</div>";
?>
