<?php
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Simulate accessing service-details.php?service=architectural-design
$_GET['service'] = 'architectural-design';

echo "=== Testing Service Detail Page Hero Logic ===\n\n";

// Step 1: Get service data (same as service-details.php)
$service_slug = $_GET['service'] ?? 'architectural-design';
echo "1. Service slug: $service_slug\n";

$current_service = getServiceBySlugWithHero($service_slug);
if (!$current_service) {
    echo "ERROR: Service not found!\n";
    exit;
}

echo "2. Service found: {$current_service['title']}\n";

// Step 2: Generate hero page name (same as service-details.php)
$hero_page_name = 'service-details-' . $current_service['slug'];
echo "3. Hero page name: $hero_page_name\n";

// Step 3: Test hero header retrieval (same as hero-header.php)
$hero_header = getHeroHeader($hero_page_name);

if (!$hero_header || !$hero_header['active']) {
    echo "ERROR: No active hero header found!\n";
    exit;
}

echo "4. Hero header found:\n";
echo "   - Title: {$hero_header['page_title']}\n";
echo "   - Subtitle: {$hero_header['subtitle']}\n";
echo "   - Background: {$hero_header['background_type']}\n";
if ($hero_header['background_image']) {
    echo "   - Background Image: {$hero_header['background_image']}\n";
}

// Step 4: Test breadcrumb logic
echo "5. Breadcrumb test:\n";
$page_name = $hero_page_name;
$current_page_title = $hero_header['page_title'];

echo "   - Home › ";
if (strpos($page_name, 'service-details-') === 0) {
    echo "Services › ";
}
echo "$current_page_title\n";

echo "\n=== SUCCESS: All logic working correctly! ===\n";
?>
