<?php
/**
 * Add title_font_size field to hero_headers table
 * Run this once to add the new column for dynamic font size control
 */

define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

try {
    $db = Database::getConnection();
    
    // Check if column already exists
    $stmt = $db->prepare("SHOW COLUMNS FROM hero_headers LIKE 'title_font_size'");
    $stmt->execute();
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        // Add the title_font_size column
        $sql = "ALTER TABLE hero_headers ADD COLUMN title_font_size VARCHAR(10) DEFAULT '3rem' AFTER title_color";
        $db->exec($sql);
        echo "✅ Successfully added title_font_size column to hero_headers table\n";
        
        // Update existing records to have the default font size
        $update_sql = "UPDATE hero_headers SET title_font_size = '3rem' WHERE title_font_size IS NULL OR title_font_size = ''";
        $db->exec($update_sql);
        echo "✅ Updated existing hero headers with default font size (3rem)\n";
    } else {
        echo "ℹ️ title_font_size column already exists in hero_headers table\n";
    }
    
    // Verify the column was added
    $stmt = $db->prepare("DESCRIBE hero_headers");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $font_size_column = null;
    foreach ($columns as $column) {
        if ($column['Field'] === 'title_font_size') {
            $font_size_column = $column;
            break;
        }
    }
    
    if ($font_size_column) {
        echo "\n📊 Column Details:\n";
        echo "   - Field: " . $font_size_column['Field'] . "\n";
        echo "   - Type: " . $font_size_column['Type'] . "\n";
        echo "   - Default: " . ($font_size_column['Default'] ?: 'NULL') . "\n";
    }
    
    echo "\n🎉 Font size control ready for hero headers!\n";
    echo "Next: Update admin interface and template to use dynamic font sizes.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
