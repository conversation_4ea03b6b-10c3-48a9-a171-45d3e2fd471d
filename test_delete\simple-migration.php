<?php
/**
 * Simple Migration Script - Direct Database Access
 */

// Database configuration (adjust as needed)
$host = 'localhost';
$dbname = 'monolith_design';
$username = 'root';
$password = 'root';

echo "<p>Attempting connection with: host=$host, dbname=$dbname, username=$username, password=" . ($password ? 'YES' : 'NO') . "</p>\n";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "<h2>Simple Admin Users Migration</h2>\n";
    
    // Create admin_users table
    $sql = "CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) NOT NULL UNIQUE,
        role ENUM('super_admin', 'contact_admin') NOT NULL DEFAULT 'contact_admin',
        status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        created_by INT NULL,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_status (status)
    )";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ admin_users table created</p>\n";
    
    // Create admin_sessions table
    $sql = "CREATE TABLE IF NOT EXISTS admin_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_id VARCHAR(128) NOT NULL UNIQUE,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
        INDEX idx_session_id (session_id),
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at)
    )";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ admin_sessions table created</p>\n";
    
    // Create admin_login_attempts table
    $sql = "CREATE TABLE IF NOT EXISTS admin_login_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50),
        ip_address VARCHAR(45) NOT NULL,
        attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        success BOOLEAN DEFAULT FALSE,
        user_agent TEXT,
        INDEX idx_username (username),
        INDEX idx_ip_address (ip_address),
        INDEX idx_attempted_at (attempted_at)
    )";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ admin_login_attempts table created</p>\n";
    
    // Insert default users
    $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $users = [
        ['admin', $defaultPassword, '<EMAIL>', 'super_admin'],
        ['contact_admin', $defaultPassword, '<EMAIL>', 'contact_admin'],
        ['support', $defaultPassword, '<EMAIL>', 'contact_admin']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO admin_users (username, password, email, role, status) VALUES (?, ?, ?, ?, 'active') ON DUPLICATE KEY UPDATE password = VALUES(password), email = VALUES(email), role = VALUES(role)");
    
    foreach ($users as $user) {
        $stmt->execute($user);
        echo "<p style='color: green;'>✓ User '{$user[0]}' created/updated</p>\n";
    }
    
    echo "<h3>Migration Complete!</h3>\n";
    echo "<p><strong>Default Login Credentials:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Super Admin: username=<code>admin</code>, password=<code>admin123</code></li>\n";
    echo "<li>Contact Admin: username=<code>contact_admin</code>, password=<code>admin123</code></li>\n";
    echo "<li>Support: username=<code>support</code>, password=<code>admin123</code></li>\n";
    echo "</ul>\n";
    
    echo "<p><a href='../admin/login.php'>→ Go to Admin Login</a></p>\n";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
