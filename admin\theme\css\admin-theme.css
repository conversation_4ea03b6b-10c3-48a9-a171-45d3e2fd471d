/**
 * Admin Theme CSS - Bootstrap 5 Based
 * Monolith Design Co. - Professional Admin Dashboard Theme
 * 
 * Design Requirements:
 * - Clean, modern design
 * - No thick left borders
 * - Soft shadows for depth
 * - Professional appearance
 */

/* ===== LOGO SIZE CONTROLS ===== */
/* Logo size control styling */
.logo-size-control {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.form-range {
    width: 100%;
    height: 6px;
    background: #ddd;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
}

.form-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.logo-size-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#logo_size_value {
    font-weight: bold;
    color: #007bff;
    font-size: 16px;
}

.size-markers {
    display: flex;
    gap: 20px;
}

.size-markers small {
    color: #666;
    font-size: 12px;
}

/* Logo preview with percentage scaling */
.logo-size-preview {
    transition: all 0.3s ease;
    max-width: 140px;
    width: 140px;
    height: auto;
}

/* ===== CSS VARIABLES ===== */
:root {
    /* Theme Colors - Will be overridden by dynamic variables */
    --admin-primary-color: #1A1A1A;
    --admin-secondary-color: #F5F5F5;
    --admin-accent-color: #E67E22;
    
    /* Typography */
    --admin-font-primary: 'Inter', sans-serif;
    --admin-font-secondary: 'Public Sans', sans-serif;
    
    /* Layout */
    --admin-header-height: 70px;
    --admin-nav-height: 60px;
    --admin-container-width: 1400px;
    --admin-content-padding: 2rem;
    --admin-border-radius: 12px;
    
    /* Shadows - Soft shadows as requested */
    --admin-shadow-soft: 0 2px 12px rgba(0, 0, 0, 0.08);
    --admin-shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.12);
    --admin-shadow-strong: 0 8px 32px rgba(0, 0, 0, 0.16);
    
    /* Spacing */
    --admin-spacing-xs: 0.5rem;
    --admin-spacing-sm: 1rem;
    --admin-spacing-md: 1.5rem;
    --admin-spacing-lg: 2rem;
    --admin-spacing-xl: 3rem;
    
    /* Transitions */
    --admin-transition-fast: 0.2s ease;
    --admin-transition-medium: 0.3s ease;
    --admin-transition-slow: 0.5s ease;
}

/* ===== GLOBAL STYLES ===== */
.admin-body {
    font-family: var(--admin-font-primary);
    background-color: #fafbfc;
    color: var(--admin-primary-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* ===== ADMIN HEADER ===== */
.admin-header {
    background: var(--admin-primary-color);
    color: white;
    height: var(--admin-header-height);
    box-shadow: var(--admin-shadow-soft);
    position: sticky;
    top: 0;
    z-index: 1030;
}

.admin-header-container {
    max-width: var(--admin-container-width);
    margin: 0 auto;
    padding: 0 var(--admin-spacing-md);
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.admin-brand {
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-sm);
}

.brand-logo .logo-image {
    height: 40px;
    width: auto;
}

.brand-title {
    font-family: var(--admin-font-secondary);
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
    color: white;
}

.brand-subtitle {
    font-size: 0.875rem;
    opacity: 0.8;
    font-weight: 400;
}

.admin-header-actions {
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-md);
}

.user-menu-toggle {
    color: white !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-xs);
    padding: 0.5rem;
    border-radius: var(--admin-border-radius);
    transition: var(--admin-transition-fast);
}

.user-menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar i {
    font-size: 1.5rem;
}

.user-dropdown {
    min-width: 280px;
    border: none;
    box-shadow: var(--admin-shadow-medium);
    border-radius: var(--admin-border-radius);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-sm);
    padding: var(--admin-spacing-sm);
}

.user-avatar-large i {
    font-size: 2rem;
    color: var(--admin-accent-color);
}

/* ===== ADMIN NAVIGATION ===== */
.admin-nav {
    background: white;
    border-bottom: 1px solid #e9ecef;
    box-shadow: var(--admin-shadow-soft);
    position: sticky;
    top: var(--admin-header-height);
    z-index: 1020;
}

.admin-nav-container {
    max-width: var(--admin-container-width);
    margin: 0 auto;
    padding: 0 var(--admin-spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--admin-nav-height);
}

.admin-nav-content {
    flex: 1;
    display: flex;
    justify-content: flex-start;
}

.admin-nav-list {
    display: flex !important; /* Ensure navigation is always visible */
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0;
    flex-wrap: nowrap;
    overflow-x: auto;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-xs);
    padding: var(--admin-spacing-sm) var(--admin-spacing-sm);
    color: var(--admin-primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: var(--admin-transition-fast);
    border-radius: 0;
    position: relative;
    white-space: nowrap;
}

.nav-link:hover {
    color: var(--admin-accent-color);
    background-color: rgba(230, 126, 34, 0.05);
}

.nav-link.active {
    color: var(--admin-accent-color);
    background-color: rgba(230, 126, 34, 0.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--admin-accent-color);
    border-radius: 2px 2px 0 0;
}

.nav-icon {
    font-size: 0.875rem;
    width: 16px;
    text-align: center;
}

.mobile-nav-toggle {
    background: none;
    border: none;
    color: var(--admin-primary-color);
    font-size: 1.25rem;
    padding: 0.5rem;
}

.nav-status {
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-xs);
    font-size: 0.875rem;
    color: #6c757d;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* ===== MAIN CONTENT ===== */
.admin-main {
    min-height: calc(100vh - var(--admin-header-height) - var(--admin-nav-height) - 80px);
    padding: var(--admin-content-padding) 0;
}

.admin-container {
    max-width: var(--admin-container-width);
    margin: 0 auto;
    padding: 0 var(--admin-spacing-md);
}

/* ===== PAGE HEADER ===== */
.admin-page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--admin-spacing-xl);
    padding: var(--admin-spacing-lg);
    background: white;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-soft);
}

.page-header-content {
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-md);
}

.page-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--admin-accent-color), #d35400);
    border-radius: var(--admin-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.page-title {
    font-family: var(--admin-font-secondary);
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: var(--admin-primary-color);
}

.page-description {
    color: #6c757d;
    margin: 0.25rem 0 0 0;
    font-size: 1rem;
}

.page-actions {
    display: flex;
    gap: var(--admin-spacing-sm);
}

/* ===== ADMIN FOOTER ===== */
.admin-footer {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: var(--admin-spacing-md) 0;
    margin-top: var(--admin-spacing-xl);
}

.admin-footer-container {
    max-width: var(--admin-container-width);
    margin: 0 auto;
    padding: 0 var(--admin-spacing-md);
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--admin-spacing-md);
    font-size: 0.875rem;
    color: #6c757d;
}

.footer-links {
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-sm);
}

.footer-link {
    color: #6c757d;
    text-decoration: none;
    transition: var(--admin-transition-fast);
}

.footer-link:hover {
    color: var(--admin-accent-color);
}

.footer-separator,
.info-separator {
    opacity: 0.5;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-sm);
}

/* ===== TABLE STYLING ===== */
.table {
    background-color: white;
    border-radius: var(--admin-border-radius);
    overflow: hidden;
    box-shadow: var(--admin-shadow-soft);
}

.table thead th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    background-color: white;
    transition: background-color 0.2s ease;
}

.table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.table tbody tr:hover {
    background-color: #e3f2fd !important;
}

.table td {
    padding: 0.875rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
    font-size: 0.875rem;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: white;
}

.table-striped > tbody > tr:nth-of-type(even) > td {
    background-color: #f8f9fa;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991.98px) {
    .admin-nav-container {
        flex-direction: column;
        align-items: stretch;
        padding: var(--admin-spacing-sm) var(--admin-spacing-md);
    }

    .admin-nav-content {
        width: 100%;
    }

    .admin-nav-list {
        flex-direction: column;
        width: 100%;
        gap: 0;
        background: white;
        border-radius: var(--admin-border-radius);
        overflow: hidden;
        box-shadow: var(--admin-shadow-soft);
    }

    .nav-link {
        padding: var(--admin-spacing-md);
        border-bottom: 1px solid #f1f3f4;
    }

    .nav-link:last-child {
        border-bottom: none;
    }

    .nav-link.active::after {
        display: none;
    }
    
    .page-header-content {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }
    
    .admin-page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--admin-spacing-md);
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    /* Better table responsiveness for mobile */
    .table-responsive {
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        vertical-align: middle;
    }

    /* Hide less important columns on small screens */
    .table th:nth-child(4), /* Phone column */
    .table td:nth-child(4) {
        display: none;
    }

    .table th:nth-child(6), /* Date column */
    .table td:nth-child(6) {
        display: none;
    }

    /* Adjust message column width */
    .message-preview {
        max-width: 120px !important;
    }

    /* Stack action buttons vertically */
    .btn-group {
        flex-direction: column;
        gap: 0.25rem;
    }

    .btn-group .btn {
        margin: 0;
    }
}

/* Medium screens (tablets) */
@media (max-width: 1199.98px) and (min-width: 768px) {
    .table th:nth-child(4), /* Show phone on tablets */
    .table td:nth-child(4) {
        display: table-cell;
    }

    .message-preview {
        max-width: 180px !important;
    }
}

/* Large screens - show all columns */
@media (min-width: 1200px) {
    .table th,
    .table td {
        display: table-cell;
    }

    .message-preview {
        max-width: 250px !important;
    }
}
