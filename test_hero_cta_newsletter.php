<?php
/**
 * Test Hero CTA Newsletter Submission
 */

echo "Testing hero CTA newsletter submission...\n";

// Simulate AJAX request
$postData = [
    'email' => '<EMAIL>',
    'source' => 'hero_cta',
    'page' => 'contact'
];

// Set up server variables for testing
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
$_POST = $postData;

// Capture output
ob_start();
include 'newsletter-signup.php';
$response = ob_get_clean();

echo "Response from newsletter-signup.php:\n";
echo $response . "\n";

// Parse JSON response
$data = json_decode($response, true);
if ($data) {
    echo "\nParsed response:\n";
    echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
    echo "Message: " . $data['message'] . "\n";
} else {
    echo "\nFailed to parse JSON response.\n";
}
?>
