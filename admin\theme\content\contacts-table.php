<?php
/**
 * Contact Submissions Table Content
 * Table content for contact submissions management
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure submissions variable is available
if (!isset($submissions)) {
    $submissions = [];
}
?>

<?php if (empty($submissions)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        No contact submissions found.
    </div>
<?php else: ?>
    <!-- Bulk Actions -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="table-info">
            <span class="text-muted">
                <i class="fas fa-list me-1"></i>
                <?php echo count($submissions); ?> contact submission(s)
            </span>
        </div>
        <div class="bulk-actions">
            <button type="button"
                    class="btn btn-danger btn-sm"
                    onclick="confirmDeleteAll()"
                    <?php echo empty($submissions) ? 'disabled' : ''; ?>>
                <i class="fas fa-trash-alt me-1"></i>
                Delete All
            </button>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-hover table-striped contacts-table">
            <thead>
                <tr>
                    <th width="50">#</th>
                    <th width="80">Type</th>
                    <th width="150">Name</th>
                    <th width="200">Email</th>
                    <th width="250">Message</th>
                    <th width="120">Date</th>
                    <th width="80">Status</th>
                    <th width="120">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $start_index = isset($GLOBALS['pagination']['offset']) ? $GLOBALS['pagination']['offset'] : 0;
                foreach ($submissions as $index => $submission):
                ?>
                    <tr class="searchable-item">
                        <td class="text-center">
                            <span class="text-muted"><?php echo $start_index + $index + 1; ?></span>
                        </td>
                        <td>
                            <?php if (!empty($submission['is_newsletter_signup'])): ?>
                                <span class="badge bg-info">
                                    <i class="fas fa-envelope me-1"></i>Newsletter
                                </span>
                            <?php else: ?>
                                <span class="badge bg-primary">
                                    <i class="fas fa-phone me-1"></i>Contact
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="single-line">
                            <strong><?php echo htmlspecialchars($submission['name']); ?></strong>
                        </td>
                        <td class="single-line">
                            <a href="mailto:<?php echo htmlspecialchars($submission['email']); ?>"
                               class="text-decoration-none">
                                <?php echo htmlspecialchars($submission['email']); ?>
                            </a>
                        </td>
                        <td class="single-line">
                            <div class="message-preview">
                                <?php
                                $message = htmlspecialchars($submission['message']);
                                echo strlen($message) > 60 ? substr($message, 0, 60) . '...' : $message;
                                ?>
                                <?php if (strlen($submission['message']) > 60): ?>
                                    <button type="button"
                                            class="btn btn-link btn-sm p-0 ms-1"
                                            data-bs-toggle="modal"
                                            data-bs-target="#messageModal<?php echo $submission['id']; ?>"
                                            title="View full message">
                                        <i class="fas fa-expand-alt"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="single-line">
                            <small class="text-muted">
                                <?php echo date('M j, Y', strtotime($submission['created_at'])); ?>
                            </small>
                        </td>
                        <td class="single-line">
                            <?php
                            $status = $submission['status'] ?? 'unread';
                            $statusClass = [
                                'unread' => 'bg-warning',
                                'read' => 'bg-info',
                                'replied' => 'bg-success'
                            ][$status] ?? 'bg-secondary';
                            ?>
                            <span class="badge <?php echo $statusClass; ?>">
                                <?php echo ucfirst($status); ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button"
                                        class="btn btn-sm btn-outline-primary"
                                        data-bs-toggle="modal"
                                        data-bs-target="#messageModal<?php echo $submission['id']; ?>"
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>

                                <?php if ($status !== 'read'): ?>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="mark_read">
                                        <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                        <button type="submit" 
                                                class="btn btn-sm btn-outline-info" 
                                                title="Mark as Read">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <?php if ($status !== 'replied'): ?>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="mark_replied">
                                        <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                        <button type="submit" 
                                                class="btn btn-sm btn-outline-success" 
                                                title="Mark as Replied">
                                            <i class="fas fa-reply"></i>
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                    <button type="submit" 
                                            class="btn btn-sm btn-outline-danger" 
                                            title="Delete Submission"
                                            data-confirm="Are you sure you want to delete this submission?">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php
    // Use the pagination data passed from the main page
    $total_items = isset($total_items) ? $total_items : count($submissions);
    $per_page = isset($per_page) ? $per_page : 20;
    include __DIR__ . '/../components/pagination.php';
    ?>

    <style>
    .contacts-table .single-line {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 0;
    }

    .contacts-table .message-preview {
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
    }

    .contacts-table td {
        vertical-align: middle;
    }

    .contacts-table .btn-group {
        white-space: nowrap;
    }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add confirmation to delete buttons
        document.querySelectorAll('button[data-confirm]').forEach(function(button) {
            button.addEventListener('click', function(e) {
                if (!confirm(this.getAttribute('data-confirm'))) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    });
    </script>

    <!-- Contact Details Modals -->
    <?php foreach ($submissions as $submission): ?>
            <div class="modal fade" id="messageModal<?php echo $submission['id']; ?>" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                Message from <?php echo htmlspecialchars($submission['name']); ?>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Email:</strong> 
                                    <a href="mailto:<?php echo htmlspecialchars($submission['email']); ?>">
                                        <?php echo htmlspecialchars($submission['email']); ?>
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <strong>Phone:</strong> 
                                    <?php if ($submission['phone']): ?>
                                        <a href="tel:<?php echo htmlspecialchars($submission['phone']); ?>">
                                            <?php echo htmlspecialchars($submission['phone']); ?>
                                        </a>
                                    <?php else: ?>
                                        Not provided
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <strong>Service:</strong> 
                                    <?php echo $submission['service'] ? htmlspecialchars($submission['service']) : 'Not specified'; ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Date:</strong> 
                                    <?php echo date('M j, Y g:i A', strtotime($submission['created_at'])); ?>
                                </div>
                            </div>
                            <hr>
                            <div class="message-content">
                                <strong>Message:</strong>
                                <div class="mt-2 p-3 bg-light rounded">
                                    <?php echo nl2br(htmlspecialchars($submission['message'])); ?>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <a href="mailto:<?php echo htmlspecialchars($submission['email']); ?>?subject=Re: Your inquiry" 
                               class="btn btn-primary">
                                <i class="fas fa-reply me-1"></i>
                                Reply via Email
                            </a>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
    <?php endforeach; ?>

<?php endif; ?>

<!-- Hidden form for delete all action -->
<form id="deleteAllForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete_all">
    <input type="hidden" name="confirm_delete_all" value="yes">
</form>

<script>
function confirmDeleteAll() {
    // Create a custom confirmation dialog
    const confirmed = confirm(
        'Are you sure you want to delete ALL contact submissions?\n\n' +
        'This action cannot be undone and will permanently remove all contact form entries and newsletter signups from the database.\n\n' +
        'Click OK to proceed or Cancel to abort.'
    );

    if (confirmed) {
        // Submit the hidden form
        document.getElementById('deleteAllForm').submit();
    }
}
</script>
