<?php
/**
 * Admin Authentication and Session Management
 * Handles role-based access control and session security
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Check if user is logged in and has valid session
 */
function isAdminLoggedIn() {
    if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
        return false;
    }
    
    // Check session timeout (24 hours)
    if (isset($_SESSION['admin_login_time']) && (time() - $_SESSION['admin_login_time']) > (24 * 60 * 60)) {
        logoutAdmin();
        return false;
    }
    
    // Verify session in database
    if (isset($_SESSION['admin_user_id'])) {
        try {
            $db = Database::getConnection();
            $stmt = $db->prepare("SELECT id FROM admin_sessions WHERE user_id = ? AND session_id = ? AND expires_at > NOW() AND is_active = TRUE");
            $stmt->execute([$_SESSION['admin_user_id'], session_id()]);

            if (!$stmt->fetch()) {
                logoutAdmin();
                return false;
            }
        } catch (Exception $e) {
            error_log("Session verification error: " . $e->getMessage());
            logoutAdmin();
            return false;
        }
    }
    
    return true;
}

/**
 * Check if user has specific role
 */
function hasAdminRole($role) {
    if (!isAdminLoggedIn()) {
        return false;
    }
    
    return isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === $role;
}

/**
 * Check if user is Super Admin
 */
function isSuperAdmin() {
    return hasAdminRole('super_admin');
}

/**
 * Check if user is Contact Admin
 */
function isContactAdmin() {
    return hasAdminRole('contact_admin');
}

/**
 * Get current admin user info
 */
function getCurrentAdminUser() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['admin_user_id'] ?? null,
        'username' => $_SESSION['admin_username'] ?? null,
        'email' => $_SESSION['admin_email'] ?? null,
        'role' => $_SESSION['admin_role'] ?? null,
        'login_time' => $_SESSION['admin_login_time'] ?? null
    ];
}

/**
 * Logout admin user and clean up session
 */
function logoutAdmin() {
    try {
        // Deactivate session in database
        if (isset($_SESSION['admin_user_id'])) {
            $db = Database::getConnection();
            $stmt = $db->prepare("UPDATE admin_sessions SET is_active = FALSE WHERE user_id = ? AND session_id = ?");
            $stmt->execute([$_SESSION['admin_user_id'], session_id()]);
        }
    } catch (Exception $e) {
        error_log("Logout cleanup error: " . $e->getMessage());
    }
    
    // Clear all session data
    $_SESSION = [];
    
    // Destroy session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Destroy session
    session_destroy();
}

/**
 * Require admin login - redirect if not logged in
 */
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

/**
 * Require specific admin role - redirect if insufficient permissions
 */
function requireAdminRole($role, $redirectTo = 'login.php') {
    requireAdminLogin();
    
    if (!hasAdminRole($role)) {
        // If contact admin trying to access non-contact pages, redirect to contacts
        if (isContactAdmin() && $redirectTo !== 'contacts.php') {
            header('Location: contacts.php');
            exit;
        }
        
        header('Location: ' . $redirectTo);
        exit;
    }
}

/**
 * Require Super Admin access
 */
function requireSuperAdmin() {
    requireAdminRole('super_admin');
}

/**
 * Check if current page is allowed for user role
 */
function isPageAllowedForRole($page) {
    if (!isAdminLoggedIn()) {
        return false;
    }
    
    $role = $_SESSION['admin_role'] ?? '';
    
    // Super admin can access everything
    if ($role === 'super_admin') {
        return true;
    }
    
    // Contact admin can only access contacts and logout
    if ($role === 'contact_admin') {
        $allowedPages = ['contacts.php', 'logout.php'];
        return in_array($page, $allowedPages);
    }
    
    return false;
}

/**
 * Get role display name
 */
function getRoleDisplayName($role) {
    switch ($role) {
        case 'super_admin':
            return 'Super Admin';
        case 'contact_admin':
            return 'Contact Admin';
        default:
            return 'Unknown Role';
    }
}

/**
 * Clean up expired sessions (call periodically)
 */
function cleanupExpiredSessions() {
    try {
        $db = Database::getConnection();
        
        // Remove expired sessions
        $stmt = $db->prepare("DELETE FROM admin_sessions WHERE expires_at < NOW()");
        $stmt->execute();
        
        // Remove old login attempts (older than 7 days)
        $stmt = $db->prepare("DELETE FROM admin_login_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stmt->execute();
        
    } catch (Exception $e) {
        error_log("Session cleanup error: " . $e->getMessage());
    }
}

// Clean up expired sessions on random requests (1% chance)
if (rand(1, 100) === 1) {
    cleanupExpiredSessions();
}
?>
