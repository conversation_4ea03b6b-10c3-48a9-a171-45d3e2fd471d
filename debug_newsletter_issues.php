<?php
/**
 * Check Newsletter System Issues
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h1>Newsletter System Debug</h1>\n";

try {
    $db = Database::getConnection();
    
    echo "<h2>Issue 1: Newsletter-Contact Integration Check</h2>\n";
    
    // Check if contact_submissions table has newsletter flag
    $stmt = $db->prepare("SHOW COLUMNS FROM contact_submissions LIKE 'is_newsletter_signup'");
    $stmt->execute();
    $newsletter_column = $stmt->fetch();
    
    if ($newsletter_column) {
        echo "✅ contact_submissions has is_newsletter_signup column\n";
        
        // Check for newsletter entries in contact_submissions
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM contact_submissions WHERE is_newsletter_signup = 1");
        $stmt->execute();
        $newsletter_contacts = $stmt->fetch()['count'];
        echo "📊 Newsletter entries in contact_submissions: $newsletter_contacts\n";
    } else {
        echo "❌ contact_submissions table missing is_newsletter_signup column\n";
    }
    
    // Check newsletter_subscribers table
    $stmt = $db->prepare("SHOW TABLES LIKE 'newsletter_subscribers'");
    $stmt->execute();
    if ($stmt->fetch()) {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM newsletter_subscribers");
        $stmt->execute();
        $subscribers = $stmt->fetch()['count'];
        echo "📊 Newsletter subscribers: $subscribers\n";
    } else {
        echo "❌ newsletter_subscribers table doesn't exist\n";
    }
    
    echo "\n<h2>Issue 2: Hero CTA Success Message Check</h2>\n";
    
    // Check hero sections with newsletter enabled
    $stmt = $db->prepare("SELECT page_name, newsletter_success_message FROM hero_sections WHERE show_newsletter_input = 1");
    $stmt->execute();
    $hero_newsletters = $stmt->fetchAll();
    
    foreach ($hero_newsletters as $hero) {
        echo "🎯 {$hero['page_name']}: '{$hero['newsletter_success_message']}'\n";
    }
    
    if (empty($hero_newsletters)) {
        echo "❌ No hero sections have newsletter enabled\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
