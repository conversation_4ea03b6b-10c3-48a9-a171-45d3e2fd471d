<?php
/**
 * Fix Hero CTA Button Colors - One-Click Solution
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>🎨 Hero CTA Button Color Fix</h1>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;} 
.success{color:green;background:#d4edda;padding:15px;border-radius:8px;margin:10px 0;border:1px solid #c3e6cb;} 
.error{color:red;background:#f8d7da;padding:15px;border-radius:8px;margin:10px 0;border:1px solid #f5c6cb;} 
.info{color:blue;background:#d1ecf1;padding:15px;border-radius:8px;margin:10px 0;border:1px solid #bee5eb;} 
.warning{color:#856404;background:#fff3cd;padding:15px;border-radius:8px;margin:10px 0;border:1px solid #ffeaa7;}
table{width:100%;border-collapse:collapse;margin:20px 0;background:white;border-radius:8px;overflow:hidden;}
th,td{padding:12px;border:1px solid #ddd;text-align:left;}
th{background:#f8f9fa;font-weight:600;}
.color-box{width:30px;height:20px;border:1px solid #ccc;display:inline-block;margin-right:8px;border-radius:4px;}
.btn{background:#E67E22;color:white;padding:12px 24px;border:none;border-radius:6px;cursor:pointer;font-weight:600;text-decoration:none;display:inline-block;}
.btn:hover{background:#d35400;}
</style>";

try {
    $db = Database::getConnection();
    
    // Auto-fix if requested
    if (isset($_GET['fix']) && $_GET['fix'] === 'now') {
        echo "<div class='info'>🔧 Fixing hero section button colors...</div>";
        
        $update_sql = "
            UPDATE hero_sections SET 
                button_bg_color = ?,
                button_text_color = ?,
                button_hover_bg_color = ?
        ";
        
        $stmt = $db->prepare($update_sql);
        $result = $stmt->execute([
            ACCENT_COLOR,     // Set button_bg_color to theme color
            '#ffffff',        // Set button_text_color to white
            '#d35400'         // Set button_hover_bg_color to darker orange
        ]);
        
        if ($result) {
            $affected_rows = $stmt->rowCount();
            echo "<div class='success'>✅ SUCCESS! Updated $affected_rows hero sections with correct theme colors!</div>";
            echo "<div class='info'>🎯 All CTA hero section buttons now use theme color: <span class='color-box' style='background:" . ACCENT_COLOR . "'></span>" . ACCENT_COLOR . "</div>";
            echo "<div class='info'>🔄 <a href='?' style='color:#007bff;'>Check Results</a> | <a href='/' style='color:#007bff;'>View Website</a></div>";
        } else {
            echo "<div class='error'>❌ Failed to update hero section colors</div>";
        }
    }
    
    // Check current status
    echo "<div class='info'>📋 Current Hero Sections Button Colors:</div>";
    
    $stmt = $db->query("SELECT page_name, button_bg_color, button_text_color, button_hover_bg_color FROM hero_sections ORDER BY page_name");
    $hero_sections = $stmt->fetchAll();
    
    if (empty($hero_sections)) {
        echo "<div class='warning'>⚠️ No hero sections found in database</div>";
    } else {
        echo "<table>";
        echo "<tr><th>Page</th><th>Button Background</th><th>Button Text</th><th>Button Hover</th><th>Status</th></tr>";
        
        $needs_fix = false;
        foreach ($hero_sections as $hero) {
            $bg_color = $hero['button_bg_color'] ?? '';
            $text_color = $hero['button_text_color'] ?? '';
            $hover_color = $hero['button_hover_bg_color'] ?? '';
            
            $is_correct = ($bg_color === ACCENT_COLOR && $text_color === '#ffffff' && $hover_color === '#d35400');
            if (!$is_correct) $needs_fix = true;
            
            $status_class = $is_correct ? 'success' : 'error';
            $status_text = $is_correct ? '✅ Correct' : '❌ Needs Fix';
            
            echo "<tr style='background:" . ($is_correct ? '#d4edda' : '#f8d7da') . ";'>";
            echo "<td><strong>{$hero['page_name']}</strong></td>";
            echo "<td><span class='color-box' style='background:{$bg_color}'></span>{$bg_color}</td>";
            echo "<td><span class='color-box' style='background:{$text_color}'></span>{$text_color}</td>";
            echo "<td><span class='color-box' style='background:{$hover_color}'></span>{$hover_color}</td>";
            echo "<td>$status_text</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Show fix button if needed
        if ($needs_fix) {
            echo "<div style='background:white;padding:30px;border-radius:12px;margin:20px 0;text-align:center;border:2px solid #E67E22;'>";
            echo "<h2 style='color:#E67E22;margin-bottom:15px;'>🛠️ Fix Required</h2>";
            echo "<p style='margin-bottom:20px;font-size:1.1rem;'>Some hero sections have incorrect button colors. Click below to fix all at once:</p>";
            echo "<a href='?fix=now' class='btn' style='font-size:1.1rem;padding:15px 30px;'>🎨 Fix All Button Colors Now</a>";
            echo "</div>";
        } else {
            echo "<div class='success' style='text-align:center;font-size:1.2rem;'>🎉 All hero section button colors are correct!</div>";
        }
    }
    
    // Show expected colors
    echo "<div class='info'>🎯 Expected Theme Colors:</div>";
    echo "<table>";
    echo "<tr><th>Property</th><th>Expected Value</th><th>Preview</th></tr>";
    echo "<tr><td>Button Background</td><td>" . ACCENT_COLOR . "</td><td><span class='color-box' style='background:" . ACCENT_COLOR . "'></span></td></tr>";
    echo "<tr><td>Button Text</td><td>#ffffff</td><td><span class='color-box' style='background:#ffffff;border:2px solid #ccc;'></span></td></tr>";
    echo "<tr><td>Button Hover</td><td>#d35400</td><td><span class='color-box' style='background:#d35400'></span></td></tr>";
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}
?>
