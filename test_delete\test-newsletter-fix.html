<!DOCTYPE html>
<html>
<head>
    <title>Newsletter Signup Test - Fixed Version</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: bold; color: #555; }
        input[type="email"], select { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; box-sizing: border-box; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; width: 100%; }
        button:hover { background: #005a87; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin-top: 15px; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin-top: 15px; border: 1px solid #f5c6cb; }
        .loading { opacity: 0.7; }
        .test-info { background: #e7f3ff; padding: 15px; border-radius: 4px; margin-bottom: 20px; border-left: 4px solid #007cba; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Newsletter Signup Test - FIXED VERSION</h1>
        
        <div class="test-info">
            <strong>🔧 NEWSLETTER FIX APPLIED!</strong><br>
            This test uses the new simplified newsletter-signup.php handler that:<br>
            • Only accepts AJAX requests<br>
            • Has better error handling<br>
            • Creates database tables automatically<br>
            • Returns proper JSON responses
        </div>
        
        <div id="statusMessages"></div>
        
        <form id="testNewsletterForm" method="POST">
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" required placeholder="Enter your email address">
            </div>
            
            <div class="form-group">
                <label for="source">Source:</label>
                <select id="source" name="source">
                    <option value="footer">Footer</option>
                    <option value="hero_cta">Hero CTA</option>
                    <option value="contact_page">Contact Page</option>
                    <option value="test_fix">Test Fix</option>
                </select>
            </div>
            
            <button type="submit" id="submitBtn">Test Newsletter Signup</button>
            <input type="hidden" name="page" value="test_fix">
        </form>
        
        <div id="responseMessage" style="display: none;"></div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
            <h3>🧪 Test Instructions:</h3>
            <ol>
                <li>Enter a valid email address</li>
                <li>Select a source from the dropdown</li>
                <li>Click "Test Newsletter Signup"</li>
                <li>Check for success/error messages</li>
                <li>Try the same email again to test duplicate handling</li>
            </ol>
            
            <h3>🔍 What This Tests:</h3>
            <ul>
                <li>✅ AJAX request handling</li>
                <li>✅ JSON response parsing</li>
                <li>✅ Database table creation</li>
                <li>✅ Email validation</li>
                <li>✅ Duplicate email handling</li>
                <li>✅ Error handling and logging</li>
            </ul>
        </div>
    </div>
    
    <script>
    function addStatus(message, type = 'info') {
        const statusDiv = document.getElementById('statusMessages');
        const statusEl = document.createElement('div');
        statusEl.className = `status ${type}`;
        statusEl.innerHTML = message;
        statusDiv.appendChild(statusEl);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (statusEl.parentNode) {
                statusEl.parentNode.removeChild(statusEl);
            }
        }, 5000);
    }
    
    document.getElementById('testNewsletterForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const responseDiv = document.getElementById('responseMessage');
        const submitBtn = document.getElementById('submitBtn');
        const email = formData.get('email');
        const source = formData.get('source');
        
        if (!email) {
            addStatus('❌ Please enter an email address', 'error');
            return;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.textContent = 'Testing...';
        this.classList.add('loading');
        responseDiv.style.display = 'none';
        
        addStatus(`🔄 Testing newsletter signup for: ${email} (source: ${source})`, 'info');
        
        console.log('Submitting to:', '/monolith-design/newsletter-signup.php');
        console.log('Form data:', Object.fromEntries(formData));
        
        fetch('/monolith-design/newsletter-signup.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', Object.fromEntries(response.headers.entries()));
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response.text();
        })
        .then(text => {
            console.log('Raw response:', text);
            
            try {
                const data = JSON.parse(text);
                console.log('Parsed JSON:', data);
                
                responseDiv.style.display = 'block';
                
                if (data.success) {
                    responseDiv.className = 'success';
                    responseDiv.innerHTML = '<strong>✅ SUCCESS!</strong><br>' + data.message;
                    addStatus(`✅ Newsletter signup successful: ${data.message}`, 'success');
                    
                    // Reset form
                    this.reset();
                } else {
                    responseDiv.className = 'error';
                    responseDiv.innerHTML = '<strong>❌ FAILED!</strong><br>' + data.message;
                    addStatus(`❌ Newsletter signup failed: ${data.message}`, 'error');
                }
            } catch (e) {
                console.error('JSON parse error:', e);
                responseDiv.style.display = 'block';
                responseDiv.className = 'error';
                responseDiv.innerHTML = '<strong>❌ INVALID RESPONSE!</strong><br>Server returned: ' + text;
                addStatus(`❌ Invalid JSON response: ${e.message}`, 'error');
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            responseDiv.style.display = 'block';
            responseDiv.className = 'error';
            responseDiv.innerHTML = '<strong>❌ NETWORK ERROR!</strong><br>' + error.message;
            addStatus(`❌ Network error: ${error.message}`, 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.textContent = 'Test Newsletter Signup';
            this.classList.remove('loading');
        });
    });
    
    // Auto-populate with a test email
    document.getElementById('email').value = 'test-fix-' + Date.now() + '@example.com';
    
    addStatus('🚀 Newsletter test form loaded and ready!', 'success');
    </script>
</body>
</html>
