<?php
/**
 * Check Current Admin Users in Database
 * This script will help diagnose the login issue
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Admin Users Database Check</h2>\n";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=monolith_design;charset=utf8', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Database connected successfully</p>\n";
    
    // Check if admin_users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ admin_users table does not exist!</p>\n";
        echo "<p>You need to run the migration script first: <a href='migrate-admin-users.php'>migrate-admin-users.php</a></p>\n";
        exit;
    }
    
    echo "<p style='color: green;'>✓ admin_users table exists</p>\n";
    
    // Get all admin users
    $stmt = $pdo->query("SELECT id, username, email, role, status, created_date, last_login FROM admin_users ORDER BY id");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p style='color: red;'>❌ No admin users found in database!</p>\n";
        echo "<p>You need to create admin users. Run: <a href='simple-migration.php'>simple-migration.php</a></p>\n";
        exit;
    }
    
    echo "<h3>📋 Current Admin Users</h3>\n";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>ID</th>";
    echo "<th style='padding: 10px;'>Username</th>";
    echo "<th style='padding: 10px;'>Email</th>";
    echo "<th style='padding: 10px;'>Role</th>";
    echo "<th style='padding: 10px;'>Status</th>";
    echo "<th style='padding: 10px;'>Created</th>";
    echo "<th style='padding: 10px;'>Last Login</th>";
    echo "</tr>\n";
    
    foreach ($users as $user) {
        $statusColor = $user['status'] === 'active' ? 'green' : 'red';
        $roleColor = $user['role'] === 'super_admin' ? 'blue' : 'orange';
        
        echo "<tr>";
        echo "<td style='padding: 10px; text-align: center;'>{$user['id']}</td>";
        echo "<td style='padding: 10px;'><strong>{$user['username']}</strong></td>";
        echo "<td style='padding: 10px;'>{$user['email']}</td>";
        echo "<td style='padding: 10px; color: {$roleColor};'><strong>{$user['role']}</strong></td>";
        echo "<td style='padding: 10px; color: {$statusColor};'><strong>{$user['status']}</strong></td>";
        echo "<td style='padding: 10px;'>{$user['created_date']}</td>";
        echo "<td style='padding: 10px;'>" . ($user['last_login'] ?: 'Never') . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Test password verification for 'admin' user
    echo "<h3>🔐 Password Testing</h3>\n";
    
    $stmt = $pdo->prepare("SELECT username, password FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $admin_user = $stmt->fetch();
    
    if ($admin_user) {
        echo "<p>Testing passwords for user: <strong>{$admin_user['username']}</strong></p>\n";
        
        // Test common passwords
        $test_passwords = [
            'admin123',
            'MonolithAdmin2025!',
            'admin',
            'password'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 10px;'>Password</th>";
        echo "<th style='padding: 10px;'>Result</th>";
        echo "</tr>\n";
        
        $working_password = null;
        foreach ($test_passwords as $password) {
            $is_valid = password_verify($password, $admin_user['password']);
            $result_color = $is_valid ? 'green' : 'red';
            $result_text = $is_valid ? '✓ VALID' : '✗ Invalid';
            
            if ($is_valid) {
                $working_password = $password;
            }
            
            echo "<tr>";
            echo "<td style='padding: 10px;'><code>{$password}</code></td>";
            echo "<td style='padding: 10px; color: {$result_color};'><strong>{$result_text}</strong></td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        if ($working_password) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #155724;'>🎉 Solution Found!</h4>\n";
            echo "<p>The correct password for user '<strong>admin</strong>' is: <code style='background: #f8f9fa; padding: 2px 6px; border-radius: 3px;'>{$working_password}</code></p>\n";
            echo "<p><a href='../admin/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>→ Try Login Now</a></p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #721c24;'>❌ No Working Password Found</h4>\n";
            echo "<p>None of the common passwords work. You may need to reset the password.</p>\n";
            echo "<p><a href='update-default-passwords.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>→ Reset Passwords</a></p>\n";
            echo "</div>\n";
        }
        
        // Show password hash for debugging
        echo "<h4>🔍 Debug Information</h4>\n";
        echo "<p><strong>Stored password hash:</strong></p>\n";
        echo "<code style='background: #f8f9fa; padding: 10px; display: block; word-break: break-all; border-radius: 3px;'>{$admin_user['password']}</code>\n";
        
    } else {
        echo "<p style='color: red;'>❌ Admin user not found!</p>\n";
    }
    
    // Check recent login attempts
    echo "<h3>📊 Recent Login Attempts</h3>\n";
    
    $stmt = $pdo->query("SELECT username, ip_address, attempted_at, success FROM admin_login_attempts ORDER BY attempted_at DESC LIMIT 10");
    $attempts = $stmt->fetchAll();
    
    if (empty($attempts)) {
        echo "<p>No login attempts recorded.</p>\n";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 10px;'>Username</th>";
        echo "<th style='padding: 10px;'>IP Address</th>";
        echo "<th style='padding: 10px;'>Time</th>";
        echo "<th style='padding: 10px;'>Result</th>";
        echo "</tr>\n";
        
        foreach ($attempts as $attempt) {
            $result_color = $attempt['success'] ? 'green' : 'red';
            $result_text = $attempt['success'] ? '✓ Success' : '✗ Failed';
            
            echo "<tr>";
            echo "<td style='padding: 10px;'>{$attempt['username']}</td>";
            echo "<td style='padding: 10px;'>{$attempt['ip_address']}</td>";
            echo "<td style='padding: 10px;'>{$attempt['attempted_at']}</td>";
            echo "<td style='padding: 10px; color: {$result_color};'><strong>{$result_text}</strong></td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Make sure your database is running and the credentials in config.php are correct.</p>\n";
}
?>
